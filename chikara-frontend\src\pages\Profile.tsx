import Button from "@/components/Buttons/Button";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import ProfileBanner from "@/components/Layout/Banners/ProfileBanner";
import Spinner from "@/components/Spinners/Spinner";
import useInviteToGang from "@/features/gang/api/useInviteToGang";
import useAddRival from "@/features/social/api/useAddRival";
import useSendFriendRequest from "@/features/social/api/useSendFriendRequest";
import useBeginBattle from "@/features/battle/api/useBeginBattle";
import { capitaliseFirstLetter } from "@/helpers/capitaliseFirstLetter";
import { timeRemaining, formatTimeToNow } from "@/helpers/dateHelpers";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import useGameConfig from "@/hooks/useGameConfig";
import { useQuery } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { useNavigate, useParams } from "react-router-dom";
import ProfileComments from "../components/Profile/ProfileComments";
import NotFoundPage from "./NotFoundPage";
import { api } from "@/helpers/api";

export default function Profile() {
    const { data: currentUser, isLoading: currentUserLoading } = useFetchCurrentUser();
    const navigate = useNavigate();
    const { id } = useParams<{ id: string }>();
    const isYourProfile = Number.parseInt(id!) === currentUser?.id;
    const { PVP_MIN_LVL, PVP_BATTLE_AP_COST } = useGameConfig();

    const { isLoading, error, data: userData } = useGetUserInfo(Number.parseInt(id!), { enabled: !isYourProfile });
    const { setStudentId, inviteToGang } = useInviteToGang();
    const { mutate: sendFriendRequest, isLoading: isSendingFriendRequest } = useSendFriendRequest();
    const { mutate: addRival, isLoading: isAddingRival } = useAddRival();
    const beginBattleMutation = useBeginBattle();

    const gangId = isYourProfile ? currentUser?.gangId : userData?.gangId;

    const { data: gangData } = useQuery(
        api.gang.getGangInfo.queryOptions({ input: { gangId }, enabled: !!gangId && !isNaN(gangId) && gangId > 0 })
    );

    const data = isYourProfile ? currentUser : userData;

    const attackFailedToast = (msg: string) => {
        toast.error(msg);
    };

    const startConversation = (userId: number) => {
        navigate(`/inbox/${userId}`);
    };

    if (!isYourProfile && isLoading) return <Spinner center />;
    if (isYourProfile && currentUserLoading) return <Spinner center />;

    if (error) return <NotFoundPage student />;

    const handleGangInvite = (studentId: number) => {
        setStudentId(studentId);
        inviteToGang();
    };

    const handleSendFriendRequest = () => {
        if (isYourProfile) return;

        sendFriendRequest({ userId: Number.parseInt(id!) });
    };

    const handleAddRival = () => {
        if (isYourProfile) return;

        addRival({ userId: Number.parseInt(id!) });
    };

    const beginBattle = async () => {
        try {
            await beginBattleMutation.mutateAsync({
                battleOpponentId: Number.parseInt(id!),
            });
            navigate("/fight");
        } catch (err: any) {
            attackFailedToast(err.message);
            console.log(err);
        }
    };

    const startBattle = async () => {
        if (currentUser.hospitalisedUntil > 0) {
            toast.error("You can't attack someone while you're in hospital!");
            return;
        }
        if (currentUser.jailedUntil > 0) {
            toast.error("You can't attack someone while you're in jail!");
            return;
        }
        if (data?.level < PVP_MIN_LVL) {
            toast.error(`You can't attack students below level ${PVP_MIN_LVL}!`);
            return;
        }
        if (currentUser?.level < PVP_MIN_LVL) {
            toast.error(`You can't attack other students until level ${PVP_MIN_LVL}!`);
            return;
        }
        if (currentUser.actionPoints < PVP_BATTLE_AP_COST) {
            toast.error("You need more AP!");
            return;
        }
        if (data?.userType === "admin") {
            toast.error("You can't attack staff members!");
            return;
        }
        await beginBattle();
    };

    return (
        <div className="">
            <ProfileBanner userData={data} isYourProfile={isYourProfile} />
            <div className="relative mx-auto mb-4 w-full max-w-3xl rounded-b-lg bg-white text-shadow shadow-sm md:flex md:items-center lg:max-w-7xl dark:border-gray-600 dark:border-x dark:border-b dark:bg-gray-800">
                <div className="mx-auto mb-4 flex flex-col items-center space-x-5 ">
                    <div className="shrink-0">
                        <div className="-mt-28 relative mb-5">
                            <DisplayAvatar className="size-40 rounded-full" src={data} />
                            <span className="absolute inset-0 rounded-full shadow-inner" aria-hidden="true" />
                            <div className="absolute bottom-3 left-3/4 flex size-10 rounded-full border-2 border-indigo-900 bg-indigo-600 text-white shadow-sm ">
                                <span className="m-auto text-lg">{data?.level}</span>
                            </div>
                        </div>
                    </div>
                    <div className="flex flex-row">
                        <div>
                            <h1 className="text-3xl text-custom-yellow text-stroke-sm">
                                {data?.username} <small className="text-gray-400">#{data?.id}</small>
                            </h1>

                            {gangData ? (
                                <p className="text-center font-medium text-gray-500 text-sm dark:text-gray-400">
                                    {data?.id === gangData.ownerId ? "Leader" : "Member"} of
                                    <span className="ml-1 text-gray-900 dark:text-red-500">{gangData?.name}</span>{" "}
                                </p>
                            ) : (
                                <p className="text-center font-medium text-gray-500 text-sm dark:text-gray-200">
                                    No Gang
                                </p>
                            )}
                            <p className="text-center font-medium text-sm ">
                                <span className="font-medium text-gray-500 text-sm dark:text-gray-400">Class </span>
                                <span className="mt-1 text-gray-900 text-sm dark:text-gray-200">{data?.class}</span>
                            </p>
                        </div>
                    </div>
                </div>

                {currentUser?.gangId && !gangData && currentUser?.userType !== "admin" ? (
                    <div className="top-8 left-6 grid grid-cols-4 gap-3 p-2 md:absolute md:grid-cols-2 md:p-0">
                        <button
                            type="button"
                            className="inline-flex cursor-pointer items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 font-medium text-sm text-white shadow-xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-100"
                            onClick={() => handleGangInvite(data?.id)}
                        >
                            Invite To Gang
                        </button>
                    </div>
                ) : null}
                {currentUser?.userType === "admin" && (
                    <div className="top-8 left-6 grid grid-cols-4 gap-3 p-2 md:absolute md:grid-cols-2 md:p-0">
                        <button
                            type="button"
                            className="inline-flex items-center justify-center rounded-md border border-transparent bg-custom-yellow px-4 py-2 font-medium text-gray-800 text-sm shadow-xs hover:bg-amber-400 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-100"
                            onClick={() =>
                                (window.location.href = `${import.meta.env.VITE_ADMIN_PANEL_URL}/users/profile/${id}`)
                            }
                        >
                            Admin Panel
                        </button>
                        <button
                            type="button"
                            className="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 font-medium text-sm text-white shadow-xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-100"
                            onClick={() =>
                                (window.location.href = `${import.meta.env.VITE_ADMIN_PANEL_URL}/actionlogs?user=${id}`)
                            }
                        >
                            Logs
                        </button>
                        <button
                            type="button"
                            className="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 font-medium text-sm text-white shadow-xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-100"
                            onClick={() =>
                                (window.location.href = `${import.meta.env.VITE_ADMIN_PANEL_URL}/users/settings/${id}`)
                            }
                        >
                            Settings
                        </button>
                        {/* <button
              type="button"
              disabled={true}
              onClick={() => navigate(`${import.meta.env.VITE_ADMIN_PANEL_URL}/moderation/${id}`)}
              className="inline-flex items-center justify-center rounded-md border border-transparent bg-gray-600 px-4 py-2 text-sm font-medium text-gray-400 shadow-xs  focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-100"
            >
              Mod History
            </button> */}
                    </div>
                )}

                {!isYourProfile ? (
                    <div className="top-8 right-6 grid grid-cols-2 gap-3 p-2 md:absolute md:p-0">
                        <Button variant="destructive" onClick={() => startBattle()}>
                            Attack
                        </Button>
                        <Button variant="primary" onClick={() => startConversation(Number.parseInt(id!))}>
                            Message
                        </Button>
                        {/* <Button disabled variant="primary">
              Trade
            </Button> */}
                        <Button variant="primary" disabled={isSendingFriendRequest} onClick={handleSendFriendRequest}>
                            Add Friend
                        </Button>
                        <Button variant="primary" disabled={isAddingRival} onClick={handleAddRival}>
                            Add Rival
                        </Button>
                        {/* <Button disabled variant="primary">
              Block
            </Button> */}
                    </div>
                ) : (
                    <div className="top-14 right-16 mt-6 flex flex-col-reverse justify-stretch space-y-4 space-y-reverse sm:flex-row-reverse sm:justify-end sm:space-x-3 sm:space-y-0 sm:space-x-reverse md:absolute md:mt-0 md:flex-row md:space-x-3">
                        <button
                            variant="primary"
                            className="inline-flex items-center justify-center rounded-b-md border border-transparent bg-blue-600 px-4 py-2 font-medium text-sm text-white shadow-xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-100 md:rounded-md"
                            onClick={() => navigate("/settings")}
                        >
                            Edit Profile
                        </button>
                    </div>
                )}
            </div>
            {data?.jailedUntil > 0 && (
                <div className="flex items-center justify-center ">
                    <p className="mx-auto divide-y divide-gray-200 rounded-md border border-gray-200 px-2 py-1 text-lg text-red-700 shadow-sm dark:border-gray-600 dark:bg-gray-900 dark:text-red-600 dark:shadow-lg">
                        Currently Jailed for {timeRemaining(data?.jailedUntil)}!
                    </p>
                </div>
            )}
            {data?.hospitalisedUntil > 0 && (
                <div className="flex items-center justify-center">
                    <p className="mx-auto divide-y divide-gray-200 rounded-md border border-gray-200 px-2 py-1 text-lg text-red-700 shadow-sm dark:border-gray-600 dark:bg-gray-900 dark:text-red-600 dark:shadow-lg">
                        Currently Hospitalised for {timeRemaining(data?.hospitalisedUntil)}!
                    </p>
                </div>
            )}
            <div className=" -mx-5 3xl:mx-auto mt-4 grid max-w-3xl grid-cols-1 gap-6 px-4 pb-6 sm:px-6 md:pb-0 lg:max-w-7xl lg:grid-flow-col-dense lg:grid-cols-2 ">
                <div className="space-y-6 lg:col-span-2 lg:col-start-1">
                    {/* Description list*/}
                    <section aria-labelledby="applicant-information-title">
                        <div className="rounded-lg bg-white shadow-sm dark:bg-gray-800">
                            <div className="px-4 py-5 sm:px-6">
                                <h2
                                    id="applicant-information-title"
                                    className="font-medium text-gray-900 text-lg leading-6 dark:text-gray-200"
                                >
                                    Student Information
                                </h2>
                            </div>
                            <div className="border-gray-200 border-t px-4 py-5 sm:px-6 dark:border-gray-600">
                                <dl className="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
                                    <div className="sm:col-span-2">
                                        <dt className="font-medium text-gray-500 text-sm dark:text-gray-400">About</dt>
                                        <dd className="mt-1 text-gray-900 text-sm dark:text-gray-200">{data?.about}</dd>
                                    </div>
                                    <div className="sm:col-span-1">
                                        <dt className="font-medium text-gray-500 text-sm dark:text-gray-400">Joined</dt>
                                        <dd className="mt-1 text-gray-900 text-sm dark:text-gray-200">
                                            {formatTimeToNow(data?.createdAt)} ago
                                        </dd>
                                    </div>
                                    <div className="sm:col-span-1">
                                        <dt className="font-medium text-gray-500 text-sm dark:text-gray-400">
                                            Occupation
                                        </dt>
                                        <dd className="mt-1 text-gray-900 text-sm dark:text-gray-200">
                                            {data?.userType === "admin"
                                                ? "Staff"
                                                : capitaliseFirstLetter(data?.userType)}
                                        </dd>
                                    </div>
                                    <div className="sm:col-span-1">
                                        <dt className="font-medium text-gray-500 text-sm dark:text-gray-400">Gender</dt>
                                        <dd className="mt-1 text-gray-900 text-sm dark:text-gray-200">-</dd>
                                    </div>
                                    <div className="sm:col-span-1">
                                        <dt className="font-medium text-gray-500 text-sm dark:text-gray-400">
                                            Relationship Status
                                        </dt>
                                        <dd className="mt-1 text-gray-900 text-sm dark:text-gray-200">Single</dd>
                                    </div>

                                    <div className="sm:col-span-2">
                                        <section aria-labelledby="achievements">
                                            <div className="rounded-lg bg-white shadow-sm dark:border dark:border-gray-600 dark:bg-gray-800">
                                                <div className="rounded-t-lg bg-gray-200 px-4 py-2 sm:px-6 dark:bg-gray-900">
                                                    <h3
                                                        id="achievements"
                                                        className="font-medium text-gray-900 text-lg leading-6 dark:text-gray-200"
                                                    >
                                                        Achievements
                                                    </h3>
                                                </div>
                                                <div className="border-gray-200 border-t px-4 py-5 sm:px-6 dark:border-gray-600 dark:text-gray-300">
                                                    No Achievements!
                                                </div>
                                            </div>
                                        </section>
                                    </div>
                                </dl>
                            </div>
                            {/* <div>
                <a
                  href="#"
                  className="block rounded-b-lg bg-gray-50 px-4 py-4 text-center text-sm font-medium text-gray-500 hover:text-gray-700"
                >
                  View All Achievements
                </a>
              </div> */}
                        </div>
                    </section>
                    <ProfileComments userID={id} />
                </div>
            </div>
        </div>
    );
}
