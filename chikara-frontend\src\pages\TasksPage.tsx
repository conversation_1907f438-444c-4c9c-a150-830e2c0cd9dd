import LoadingState from "@/components/LoadingState";
import useGetActiveQuestList from "@/features/tasks/api/useGetActiveQuestList";
import useGetAvailableQuestList from "@/features/tasks/api/useGetAvailableQuestList";
import useGetCompletedQuestList from "@/features/tasks/api/useGetCompletedQuestList";
import useGetStoryQuests from "@/features/tasks/api/useGetStoryQuests";
import DailyTasksBanner from "@/features/tasks/components/DailyTasksBanner";
import QuestAccordion from "@/features/tasks/components/QuestAccordion";
import QuestSortAndFilterControls from "@/features/tasks/components/QuestSortAndFilterControls";
import StoryModeBanner from "@/features/tasks/components/StoryModeBanner";
import TaskStatusTabs from "@/features/tasks/components/TaskStatusTabs";
import { shopkeeperMockData } from "@/features/tasks/helpers/tasksHelpers";
import useQuestSortAndFilter, { getQuestStatus } from "@/features/tasks/hooks/useQuestSortAndFilter";
import useGetInventory from "@/hooks/api/useGetInventory";
import type { QuestWithProgress } from "@/types/quest";
import { useQuery } from "@tanstack/react-query";
import { BookOpen } from "lucide-react";
import { useState } from "react";
import { api } from "@/helpers/api";

type QuestTab = "current" | "completed" | "story";

export default function QuestsPanel() {
    const { data: questGivers, isLoading: isQuestGiversLoading } = useQuery(
        api.shop.shopList.queryOptions({ staleTime: Number.POSITIVE_INFINITY, placeholderData: shopkeeperMockData })
    );

    const { data: activeQuests, isLoading: isActiveQuestsLoading } = useGetActiveQuestList();
    const { data: completedQuests, isLoading: isCompletedQuestsLoading } = useGetCompletedQuestList();
    const { data: availableQuests, isLoading: isAvailableQuestsLoading } = useGetAvailableQuestList();
    const { data: storyQuests, isLoading: isStoryQuestsLoading } = useGetStoryQuests();
    const { data: inventory } = useGetInventory();

    const [activeTab, setActiveTab] = useState<QuestTab>("current");
    const [expandedQuests, setExpandedQuests] = useState<number[]>([]);
    const [showFilters, setShowFilters] = useState(false);

    // Mark daily quests and filter out story quests from main lists
    const currentQuests = [...(activeQuests || []), ...(availableQuests || [])];
    const currentQuestsWithoutStory = currentQuests.filter((quest) => !quest.isStoryQuest);
    const completedQuestsWithoutStory = (completedQuests || []).filter((quest) => !quest.isStoryQuest);

    // Get story quests for pinning (only for current and completed tabs)
    const pinnedStoryQuests = activeTab !== "story" ? storyQuests || [] : [];

    const unfilteredQuests =
        activeTab === "current"
            ? currentQuestsWithoutStory
            : activeTab === "story"
              ? storyQuests || []
              : completedQuestsWithoutStory;

    const {
        filteredAndSortedQuests: displayedQuests,
        sortOption,
        setSortOption,
        sortDirection,
        setSortDirection,
        filterOptions,
        setFilterOptions,
    } = useQuestSortAndFilter(unfilteredQuests as QuestWithProgress[]);

    const isLoading =
        isQuestGiversLoading ||
        isActiveQuestsLoading ||
        isCompletedQuestsLoading ||
        isAvailableQuestsLoading ||
        isStoryQuestsLoading;

    return (
        <div className="space-y-3 p-2 max-w-(--breakpoint-md) mx-auto relative">
            {/* Status Filter Tabs */}
            <TaskStatusTabs
                activeTab={activeTab}
                setActiveTab={setActiveTab}
                currentQuestLength={currentQuestsWithoutStory?.length || 0}
                completedQuestLength={completedQuestsWithoutStory?.length || 0}
                storyQuestLength={storyQuests?.length || 0}
                setFilterOptions={setFilterOptions}
            />

            {/* Daily Quests Indicator */}
            {activeTab === "current" && <DailyTasksBanner />}

            {/* Story Mode Indicator */}
            {activeTab === "story" && <StoryModeBanner />}

            {/* Sort and Filter Controls */}

            <LoadingState isLoading={isLoading} size={16}>
                {/* Pinned Story Quests (for current and completed tabs) */}
                {pinnedStoryQuests.length > 0 && (
                    <div className="mb-4">
                        <QuestAccordion
                            isPinned
                            showStatusGroups={false}
                            questGivers={questGivers || []}
                            availableQuests={availableQuests || []}
                            inventory={inventory || []}
                            expandedQuests={expandedQuests}
                            setExpandedQuests={setExpandedQuests}
                            title="Story Progress"
                            titleIcon={<BookOpen className="size-4" />}
                            quests={
                                pinnedStoryQuests.filter((quest) => {
                                    const status = getQuestStatus(quest);
                                    // Only show story quests that are available, in progress, or ready to complete
                                    return status !== "complete";
                                }) as any
                            }
                        />
                    </div>
                )}

                <QuestSortAndFilterControls
                    sortOption={sortOption}
                    setSortOption={setSortOption}
                    sortDirection={sortDirection}
                    setSortDirection={setSortDirection}
                    filterOptions={filterOptions}
                    setFilterOptions={setFilterOptions}
                    questGivers={questGivers || []}
                    showFilters={showFilters}
                    setShowFilters={setShowFilters}
                    activeTab={activeTab}
                />

                {/* Main Quests List */}
                {displayedQuests?.length === 0 ? (
                    <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 text-center">
                        <p className="text-gray-400">No tasks to show!</p>
                    </div>
                ) : (
                    <QuestAccordion
                        showStatusGroups
                        quests={displayedQuests as any}
                        questGivers={questGivers || []}
                        availableQuests={availableQuests as any}
                        inventory={inventory as any}
                        expandedQuests={expandedQuests}
                        setExpandedQuests={setExpandedQuests}
                    />
                )}
            </LoadingState>
        </div>
    );
}
