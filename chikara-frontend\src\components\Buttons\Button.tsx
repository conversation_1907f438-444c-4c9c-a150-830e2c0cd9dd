import React from "react";
import { type VariantProps, tv } from "tailwind-variants";

const button3DVariants = tv({
    slots: {
        button: [
            "group relative mt-1 cursor-pointer border-0 bg-transparent p-0 text-white outline-offset-4",
            "transition-all duration-[600ms] ease-out select-none",
            "hover:brightness-110",
            "focus:outline-none focus-visible:outline-2 focus-visible:outline-offset-4 focus-visible:outline-blue-500",
            "disabled:pointer-events-none disabled:grayscale",
        ],
        shadow: [
            "absolute top-0 left-0 size-full bg-black/25",
            "translate-y-[2.5px] transition-all duration-[600ms] cubic-bezier-[0.3,0.7,0.4,1]",
            // Group states for shadow
            "group-hover:translate-y-[1.5px] group-hover:duration-250 group-hover:ease-[cubic-bezier(0.3,0.7,0.4,1.5)]",
            "group-active:translate-y-[0.5px] group-active:duration-34",
        ],
        edge: ["absolute inset-0 m-0 size-full", "leading-[calc(1em+0.725rem)]"],
        front: [
            "relative flex items-center justify-center font-medium hover:brightness-110",
            "-translate-y-[5px] transition-all duration-[600ms] cubic-bezier-[0.3,0.7,0.4,1]",
            // Group states for front
            "group-hover:-translate-y-[4px] group-hover:duration-250 group-hover:ease-[cubic-bezier(0.3,0.7,0.4,1.5)]",
            "group-active:-translate-y-[1px] group-active:duration-34",
        ],
    },
    variants: {
        variant: {
            primary: {
                edge: "bg-gradient-to-l from-[#0c3c73] via-[#115692] to-[#0c3c73]",
                front: "bg-[#1e88e5] hover:bg-[#187dd6] text-stroke-sm",
            },
            secondary: {
                edge: "bg-gradient-to-l from-[#4a5568] via-[#718096] to-[#4a5568]",
                front: "bg-[#718096] hover:bg-[#4a5568] text-white text-stroke-sm",
            },
            destructive: {
                edge: "bg-gradient-to-l from-[#660022] via-[#a30036] to-[#660022]",
                front: "bg-[#ed003b] hover:bg-[#e00038] text-stroke-sm",
            },
            outline: {
                edge: "bg-gradient-to-l from-[#374151] via-[#6b7280] to-[#374151]",
                front: "bg-white hover:bg-gray-50 text-gray-900 border border-gray-200 text-stroke-sm",
            },
            ghost: {
                edge: "bg-gradient-to-l from-[#374151] via-[#6b7280] to-[#374151]",
                front: "bg-transparent hover:bg-gray-100 text-gray-900",
            },
            warning: {
                edge: "bg-gradient-to-l from-[#654a01] via-[#a27702] to-[#654a01]",
                front: "bg-[#fdca40] hover:bg-[#fdc221] text-black",
            },
            flat: {
                button: "mt-0", // Remove top margin for flat buttons
                shadow: "hidden", // Hide shadow for flat variant
                edge: "hidden", // Hide edge for flat variant
                front: [
                    "primaryBtn justify-center rounded-md border border-transparent font-display font-semibold text-sm shadow-xs",
                    "focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-400 dark:text-stroke-sm",
                    "transform-none", // Remove 3D transforms
                    "hover:transform-none active:transform-none", // Disable transform on hover/active
                ],
            },
        },
        size: {
            sm: {
                button: "min-w-16",
                front: "h-8 px-3 text-sm",
            },
            md: {
                button: "min-w-20",
                front: "h-10 px-4 text-sm",
            },
            lg: {
                button: "min-w-24",
                front: "h-12 px-6 text-base",
            },
            xl: {
                button: "min-w-28",
                front: "h-14 px-8 text-lg",
            },
        },
        fullWidth: {
            true: {
                button: "w-full",
            },
        },
        rounded: {
            none: "",
            sm: "rounded-sm",
            md: "rounded-md",
            lg: "rounded-lg",
            xl: "rounded-xl",
            full: "rounded-full",
        },
        disabled: {
            true: {
                front: "text-gray-400",
            },
        },
    },
    // This applies the `rounded` classes to all the necessary child elements.
    compoundVariants: [
        ...["sm", "md", "lg", "xl", "full"].map((round) => ({
            rounded: round as "sm" | "md" | "lg" | "xl" | "full",
            class: {
                shadow: `rounded-${round}`,
                edge: `rounded-${round}`,
                front: `rounded-${round}`,
            },
        })),
        // Special handling for flat variant disabled state
        {
            variant: "flat",
            disabled: true,
            class: {
                front: "bg-slate-500 text-gray-300",
            },
        },
    ],
    defaultVariants: {
        variant: "primary",
        size: "md",
        rounded: "md",
        disabled: false,
    },
});

interface ButtonProps
    extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, "disabled">,
        VariantProps<typeof button3DVariants> {
    children: React.ReactNode;
    isLoading?: boolean;
    leftIcon?: React.ReactNode;
    rightIcon?: React.ReactNode;
    disabled?: boolean;
    textSize?: string; // Allow custom text size override
    ref?: React.Ref<HTMLButtonElement>; // Add ref as a regular prop
}

const Button = ({
    className,
    variant,
    size,
    fullWidth,
    rounded,
    children,
    isLoading = false,
    leftIcon,
    rightIcon,
    disabled = false,
    onClick,
    textSize,
    ref,
    ...props
}: ButtonProps) => {
    const styles = button3DVariants({
        variant,
        size,
        fullWidth,
        rounded,
        disabled: disabled || isLoading,
    });

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
        if (disabled || isLoading) {
            e.preventDefault();
            return;
        }
        onClick?.(e);
    };

    return (
        <>
            <button
                ref={ref}
                className={styles.button({ className })}
                disabled={disabled || isLoading}
                onClick={handleClick}
                {...props}
                type="button"
            >
                <span className={styles.shadow()}></span>
                <span className={styles.edge()}></span>
                <span
                    className={`${styles.front()} ${textSize ? textSize : ""} relative`}
                    style={textSize ? { fontSize: undefined } : {}}
                >
                    {/* Always render content to maintain button size */}
                    <span className={isLoading ? "invisible" : ""}>
                        {leftIcon && <span className="mr-2">{leftIcon}</span>}
                        {children}
                        {rightIcon && <span className="ml-2">{rightIcon}</span>}
                    </span>
                    {/* Loading spinner positioned absolutely on top */}
                    {isLoading && (
                        <svg
                            className="animate-spin h-4 w-4 absolute inset-0 m-auto"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                        >
                            <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                            />
                            <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            />
                        </svg>
                    )}
                </span>
            </button>
        </>
    );
};

Button.displayName = "Button";
export default Button;
