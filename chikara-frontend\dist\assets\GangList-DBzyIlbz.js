import{r as n,b as x,c as u,t as g,j as e,o as m,G as p,L as h,d0 as j}from"./index-UBkgY7aq.js";import{V as G}from"./ViewGangModal-Cm6TqzUI.js";function y(){const[r,a]=n.useState(!1),[l,t]=n.useState(null),{data:o,isLoading:c}=x(u.gang.getGangList.queryOptions()),{data:i}=g(),d=s=>{t(s),a(!0)};return e.jsx(e.Fragment,{children:e.jsx("section",{className:"mx-auto rounded-lg bg-gray-100 py-3 md:max-w-3xl dark:bg-gray-800",children:e.jsxs("div",{className:"flex flex-col gap-2 p-1.5",children:[e.jsxs("div",{className:"relative mb-6 text-center text-2xl text-custom-yellow",children:[e.jsxs(m,{to:-1,children:[" ",e.jsx(p,{className:"!absolute left-0! h-10!",children:"Back"})]}),e.jsx("p",{children:"Gang List"})]}),e.jsxs(h,{isLoading:c,children:[e.jsx(G,{open:r,setOpen:a,selectedGang:l,setSelectedGang:t,currentUser:i}),o?.map(s=>e.jsx(j,{gang:s,className:"h-24",onClick:()=>d(s)},s.id))]})]})})})}export{y as default};
