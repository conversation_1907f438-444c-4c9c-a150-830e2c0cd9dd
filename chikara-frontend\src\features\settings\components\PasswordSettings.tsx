import { User } from "@/types/user";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { changePassword } from "../../../lib/auth-client";

interface PasswordSettingsProps {
    currentUser?: User;
}

export default function PasswordSettings({ currentUser }: PasswordSettingsProps) {
    const [currentPassword, setCurrentPassword] = useState<string>("");
    const [newPassword, setNewPassword] = useState<string>("");
    const [confirmPassword, setConfirmPassword] = useState<string>("");

    const updatePassword = async (e: React.FormEvent<HTMLFormElement>): Promise<void> => {
        e.preventDefault();
        if (newPassword !== confirmPassword) {
            toast.error("These passwords do not match!");
            return;
        }
        if (newPassword === currentPassword) {
            toast.error("These passwords are the same!");
            return;
        }

        try {
            const { data, error } = await changePassword({
                newPassword: newPassword,
                currentPassword: currentPassword,
            });

            if (data && !error) {
                toast.success("Changes Saved successfully!");
                setCurrentPassword("");
                setNewPassword("");
                setConfirmPassword("");
            } else {
                if (error?.message) {
                    toast.error(error.message);
                } else {
                    toast.error("Error saving changes!");
                }
                console.error(error);
            }
        } catch (error) {
            toast.error("An unexpected error occurred!");
            console.error(error);
        }
    };

    return (
        <>
            <form className="divide-y divide-gray-200 lg:col-span-9 dark:divide-gray-600" onSubmit={updatePassword}>
                <div className="px-4 py-2 sm:p-6 md:py-6 lg:pb-8">
                    <div>
                        <h2 className="font-medium text-gray-900 text-lg text-stroke-sm leading-6 dark:text-gray-200">
                            Password
                        </h2>
                    </div>
                    <div className="mt-6 flex flex-col lg:flex-row">
                        <div className="grow space-y-6">
                            <div>
                                <label
                                    htmlFor="username"
                                    className="mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300"
                                >
                                    Current Password
                                </label>
                                <div className="mt-1 flex rounded-md shadow-xs">
                                    <input
                                        value={currentPassword}
                                        type="password"
                                        name="currentPassword"
                                        autoComplete="password"
                                        className="block w-full min-w-0 grow rounded-md border-gray-300 focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200"
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                            setCurrentPassword(e.target.value);
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="mt-6 flex flex-col lg:flex-row">
                        <div className="grow space-y-6">
                            <div>
                                <label
                                    htmlFor="username"
                                    className="mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300"
                                >
                                    New Password
                                </label>
                                <div className="mt-1 flex rounded-md shadow-xs">
                                    <input
                                        value={newPassword}
                                        type="password"
                                        name="newPassword"
                                        autoComplete="password"
                                        className="block w-full min-w-0 grow rounded-md border-gray-300 focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200"
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                            setNewPassword(e.target.value);
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="mt-6 flex flex-col lg:flex-row">
                        <div className="grow space-y-6">
                            <div>
                                <label
                                    htmlFor="username"
                                    className="mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300"
                                >
                                    Confirm New Password
                                </label>
                                <div className="mt-1 flex rounded-md shadow-xs">
                                    <input
                                        value={confirmPassword}
                                        type="password"
                                        name="confirmPassword"
                                        autoComplete="password"
                                        className="block w-full min-w-0 grow rounded-md border-gray-300 focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200"
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                            setConfirmPassword(e.target.value);
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="mt-4 flex justify-end p-4 sm:px-6">
                    <button
                        type="submit"
                        className="ml-5 inline-flex w-1/5 justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-stroke-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2"
                    >
                        Save
                    </button>
                </div>
            </form>
        </>
    );
}
