import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

export const useClaimCompletionReward = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.dailyQuest.claimDailyCompletionReward.mutationOptions({
            onSuccess: () => {
                toast.success(`You received 1x Daily Chest!`);
                // TODO - set the dailyQuestsRewardClaimed manually
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
            },
            onError: (error: Error) => {
                toast.error(`Error: ${error.message}`);
            },
        })
    );
};
