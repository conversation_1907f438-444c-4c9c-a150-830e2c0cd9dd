import Button from "@/components/Buttons/Button";
import { displayMissingIcon } from "@/helpers/displayMissingIcon";
import { api } from "@/helpers/api";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { Search } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { toast } from "react-hot-toast";

const PetsPageSection = () => {
    const queryClient = useQueryClient();
    const [selectedItemId, setSelectedItemId] = useState<number | null>(null);
    const [searchTerm, setSearchTerm] = useState<string>("");
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const comboboxRef = useRef<HTMLDivElement>(null);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (comboboxRef.current && !comboboxRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    // Fetch all items
    const { data: items, isLoading } = useQuery(
        api.items.getItemList.queryOptions({
            staleTime: 300000, // 5 minutes
        })
    );

    // Filter items based on search term
    const filteredItems =
        items?.filter(
            (item) =>
                item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.itemType.toLowerCase().includes(searchTerm.toLowerCase())
        ) || [];

    // Mutation hooks
    const hatchEggsMutation = useMutation(
        api.dev.hatchEggs.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.pets.list.key() });
                toast.success("All eggs hatched successfully");
            },
            onError: (error) => {
                console.error("Error hatching eggs:", error);
                toast.error("Failed to hatch eggs");
            },
        })
    );

    const setFullPetHappinessMutation = useMutation(
        api.dev.setFullPetHappiness.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.pets.list.key() });
                toast.success("Pet happiness set to full");
            },
            onError: (error) => {
                console.error("Error setting pet happiness:", error);
                toast.error("Failed to set pet happiness");
            },
        })
    );

    const addPetXpMutation = useMutation(
        api.dev.addPetXp.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.pets.list.key() });
                toast.success("Pet XP added successfully");
            },
            onError: (error) => {
                console.error("Error adding pet XP:", error);
                toast.error("Failed to add pet XP");
            },
        })
    );

    const hatchEggs = () => {
        hatchEggsMutation.mutate({});
    };

    const setFullPetHappiness = () => {
        setFullPetHappinessMutation.mutate({});
    };

    const addPetXp = () => {
        addPetXpMutation.mutate({ xp: 100 });
    };

    const handleAddItem = async () => {
        if (!selectedItemId) {
            toast.error("Please select an item");
            return;
        }

        try {
            await api.dev.addItem.call({
                itemId: selectedItemId,
                quantity: 1,
            });

            queryClient.invalidateQueries({ queryKey: api.user.getInventory.key() });
            toast.success(`Successfully added item(s)`);

            // Reset form
            setSelectedItemId(null);
            setSearchTerm("");
        } catch (error) {
            console.error("Error adding item:", error);
            toast.error("Failed to add item");
        }
    };

    const handleItemSelect = (itemId: number) => {
        setSelectedItemId(itemId);
        const selectedItem = items?.find((item) => item.id === itemId);
        if (selectedItem) {
            setSearchTerm(selectedItem.name);
        }
        setIsOpen(false);
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
        setIsOpen(true);
        if (!e.target.value) {
            setSelectedItemId(null);
        }
    };

    return (
        <div className="flex flex-col gap-4 p-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <Button className="text-sm!" variant="primary" onClick={hatchEggs}>
                    Hatch Eggs
                </Button>
                <Button className="text-sm!" variant="primary" onClick={addPetXp}>
                    +100 Pet XP
                </Button>
                <Button className="text-sm!" variant="primary" onClick={setFullPetHappiness}>
                    Full Pet Happiness
                </Button>
            </div>

            <div className="mt-4 p-4 border border-gray-700 rounded-md bg-gray-800">
                {isLoading ? (
                    <p className="text-center">Loading items...</p>
                ) : (
                    <div className="flex flex-col space-y-4">
                        <div className="flex flex-col space-y-2">
                            <label htmlFor="item-search" className="text-sm font-medium text-gray-200">
                                Search & Select Pet
                            </label>
                            <div ref={comboboxRef} className="relative">
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                        <Search className="size-4 text-gray-400" />
                                    </div>
                                    <input
                                        id="item-search"
                                        type="text"
                                        className="rounded-md border border-gray-600 bg-gray-800 pl-10 p-2 text-gray-200 w-full"
                                        placeholder="Search items..."
                                        value={searchTerm}
                                        onChange={handleSearchChange}
                                        onClick={() => setIsOpen(true)}
                                    />
                                </div>

                                {isOpen && (
                                    <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md border border-gray-700 bg-gray-800 py-1 shadow-lg">
                                        {filteredItems.length > 0 ? (
                                            filteredItems.map((item) => (
                                                <div
                                                    key={item.id}
                                                    className={`cursor-pointer px-4 py-2 hover:bg-gray-700 ${
                                                        selectedItemId === item.id ? "bg-blue-600" : ""
                                                    }`}
                                                    onClick={() => handleItemSelect(item.id)}
                                                >
                                                    <div className="flex items-center gap-3">
                                                        <img src={displayMissingIcon(item.image)} className="size-5" />
                                                        <div className="flex flex-col">
                                                            <span className="text-sm">{item.name}</span>
                                                            <span className="text-gray-400 text-xs">ID: {item.id}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="px-4 py-2 text-sm text-gray-400">No items found</div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className="mt-2">
                            <Button
                                className="w-full"
                                variant="primary"
                                disabled={!selectedItemId}
                                onClick={handleAddItem}
                            >
                                Add Pet
                            </Button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default PetsPageSection;
