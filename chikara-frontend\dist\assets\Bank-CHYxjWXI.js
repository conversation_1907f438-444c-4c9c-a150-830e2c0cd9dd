import{r as m,j as e,h as k,x as E,I as D,u as S,y as d,B as G,e as w,g as v,c,b as H,o as P,v as V,t as M,S as z,f as C,C as X,F as J,G as I,l as B}from"./index-UBkgY7aq.js";import{C as Z}from"./circle-alert-D0c1muDS.js";import{C as ee}from"./circle-x-BDG2Oap6.js";import{C as te}from"./circle-check-big-C3bhZzKj.js";import{C as ae}from"./Callout-j9j_63dc.js";const re=E("relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",success:"border-green-500/50 text-green-700 bg-green-50 dark:bg-green-900/20 dark:border-green-500 dark:text-green-300 [&>svg]:text-green-600 dark:[&>svg]:text-green-400",warning:"border-yellow-500/50 text-yellow-700 bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-500 dark:text-yellow-300 [&>svg]:text-yellow-600 dark:[&>svg]:text-yellow-400",info:"border-blue-500/50 text-blue-700 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-500 dark:text-blue-300 [&>svg]:text-blue-600 dark:[&>svg]:text-blue-400"}},defaultVariants:{variant:"default"}}),_=m.forwardRef(({className:t,variant:s,...a},n)=>e.jsx("div",{ref:n,role:"alert",className:k(re({variant:s}),t),...a}));_.displayName="Alert";const W=m.forwardRef(({className:t,...s},a)=>e.jsx("h5",{ref:a,className:k("mb-1 font-medium leading-none tracking-tight",t),...s}));W.displayName="AlertTitle";const L=m.forwardRef(({className:t,...s},a)=>e.jsx("div",{ref:a,className:k("text-sm [&_p]:leading-relaxed",t),...s}));L.displayName="AlertDescription";const se=({variant:t})=>{switch(t){case"success":return e.jsx(te,{className:"h-4 w-4"});case"destructive":return e.jsx(ee,{className:"h-4 w-4"});case"warning":return e.jsx(Z,{className:"h-4 w-4"});case"info":return e.jsx(D,{className:"h-4 w-4"});default:return e.jsx(D,{className:"h-4 w-4"})}},ne=({transactionType:t,amount:s,transferID:a,TRANSACTION_FEE:n=0,MINIMUM_WITHDRAWAL:u=0,MINIMUM_DEPOSIT:o=0,MINIMUM_TRANSFER:x=0,className:h})=>{const f=a?Number.parseInt(String(a)):void 0,{data:p}=S(f,{enabled:!!f&&t==="Transfer"}),r=m.useMemo(()=>{const l=d(s),y=G(s,n),g=d(y);switch(t){case"Deposit":return{variant:"success",title:`Successfully deposited ${l}!`,description:n>0?`A ${n}% deposit fee of ${g} was taken by the bank.`:void 0};case"Withdraw":return{variant:"success",title:`Successfully withdrew ${l}!`};case"Transfer":return{variant:"success",title:`Successfully transferred ${l} to ${p?.username||"recipient"}!`,description:n>0?`A ${n}% transfer fee of ${g} was taken by the bank.`:void 0};case"DepositAmountTooLow":return{variant:"destructive",title:`Minimum deposit amount is ${d(o)}`};case"WithdrawAmountTooLow":return{variant:"destructive",title:`Minimum withdrawal amount is ${d(u)}`};case"TransferAmountTooLow":return{variant:"destructive",title:`Minimum transfer amount is ${d(x)}`};case"InvalidDepositAmount":return{variant:"destructive",title:"Insufficient funds for deposit",description:"You don't have enough cash to make this deposit."};case"InvalidWithdrawAmount":return{variant:"destructive",title:"Insufficient bank balance",description:"You don't have enough money in your bank account for this withdrawal."};default:return{variant:"info",title:"Transaction processed"}}},[t,s,p?.username,n,o,u,x]);return e.jsxs(_,{variant:r.variant,className:h,children:[e.jsx(se,{variant:r.variant}),e.jsx(W,{children:r.title}),r.description&&e.jsx(L,{children:r.description})]})},ie=()=>{const t=w();return v(c.bank.deposit.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:c.user.getCurrentUserInfo.key()}),t.invalidateQueries({queryKey:c.bank.getBankTransactions.key()})}}))},oe=()=>{const t=w();return v(c.bank.transfer.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:c.user.getCurrentUserInfo.key()}),t.invalidateQueries({queryKey:c.bank.getBankTransactions.key()})}}))},le=()=>{const t=w();return v(c.bank.withdraw.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:c.user.getCurrentUserInfo.key()}),t.invalidateQueries({queryKey:c.bank.getBankTransactions.key()})}}))},de=()=>H(c.bank.getBankTransactions.queryOptions());function ce({transaction:t,currentUser:s}){let a;t.initiatorId===s?a=t.secondPartyId:a=t.initiatorId;const{data:n}=S(a,{enabled:!!a});return e.jsx("td",{className:"whitespace-nowrap p-2 font-medium text-gray-900 text-sm dark:text-gray-300",children:e.jsxs("span",{children:["Bank transfer ",t.initiatorId===s?"to":"from"," ",e.jsxs(P,{className:"inline text-blue-600",to:`/profile/${a}`,children:[" ",n?.username]})]})})}const me=({historyLimit:t=10})=>{const s=V(),{isLoading:a,error:n,data:u}=de(),{data:o}=M(),x=u?u.slice(0,t):[];if(a)return"Loading...";if(n)return"An error has occurred: "+n.message;const h=r=>{let l;return s?l=C(r,"dd/MM kk:mm"):l=C(r,"dd/MM/y kk:mm"),l},f=r=>r.transaction_type==="bank_withdrawl"?"Bank Withdrawal":r.transaction_type==="bank_deposit"?"Bank Deposit":r.transaction_type,p=r=>r.transaction_type==="bank_transfer"?r.playerId===o.id?e.jsxs("td",{className:"whitespace-nowrap p-2 text-green-500 text-sm",children:["+",d(r.cash)]}):e.jsxs("td",{className:"whitespace-nowrap p-2 text-red-500 text-sm",children:["-",d(r.cash)]}):e.jsx("td",{className:"whitespace-nowrap p-2 text-gray-900 text-sm dark:text-gray-300",children:d(r.cash)});return e.jsxs("div",{className:"mb-5 px-4 sm:px-6 lg:px-8",children:[e.jsx("div",{className:"sm:flex sm:items-center",children:e.jsx("div",{className:"sm:flex-auto",children:e.jsxs("h1",{className:"font-semibold text-gray-900 md:text-xl dark:font-normal dark:text-gray-200 dark:text-stroke-s-sm",children:["Transaction History (Last ",t," Transactions)"]})})}),e.jsx("div",{className:"mt-4 flex flex-col md:mt-8",children:e.jsx("div",{className:"-mx-4 -my-2 sm:-mx-6 lg:-mx-8 overflow-x-auto",children:e.jsx("div",{className:"inline-block min-w-full py-2 align-middle md:px-6 lg:px-8",children:e.jsx("div",{className:"overflow-hidden shadow-sm ring-1 ring-black/5 md:rounded-lg",children:a?e.jsx(z,{}):e.jsxs("table",{className:"min-w-full divide-y divide-gray-300",children:[e.jsx("thead",{className:"bg-gray-50 text-gray-900 text-sm dark:bg-gray-800 dark:text-gray-200 dark:text-stroke-sm",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"whitespace-nowrap py-3.5 pr-3 pl-4 text-left font-semibold sm:pl-6 dark:font-normal",children:"Date"}),e.jsx("th",{scope:"col",className:"whitespace-nowrap px-2 py-3.5 text-left font-semibold dark:font-normal",children:"Type"}),e.jsx("th",{scope:"col",className:"whitespace-nowrap px-2 py-3.5 text-left font-semibold dark:font-normal",children:"Amount"}),e.jsx("th",{scope:"col",className:"hidden whitespace-nowrap px-2 py-3.5 text-left font-semibold md:table-cell dark:font-normal",children:"Bank Balance"})]})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white dark:divide-gray-600 dark:bg-gray-700",children:x.map((r,l)=>e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap py-2 pr-3 pl-4 text-gray-500 text-sm sm:pl-6 md:pr-1 dark:text-gray-300",children:h(r.createdAt)}),r.transaction_type==="bank_transfer"?e.jsx(ce,{transaction:r,currentUser:o.id}):e.jsx("td",{className:"whitespace-nowrap p-2 font-medium text-gray-900 text-sm dark:text-gray-300",children:f(r)}),p(r),e.jsx("td",{className:"hidden whitespace-nowrap p-2 text-gray-900 text-sm md:table-cell dark:text-gray-300",children:d(r.bankBalance)})]},l))})]})})})})})]})};function ge(){const[t,s]=m.useState(0),[a,n]=m.useState("Deposit"),[u,o]=m.useState(void 0),[x,h]=m.useState(0),[f,p]=m.useState(0),{data:r}=M(),l=ie(),y=le(),g=oe(),{MINIMUM_WITHDRAWAL:j,MINIMUM_DEPOSIT:N,MINIMUM_TRANSFER:A,TRANSACTION_HISTORY_LIMIT:$,TRANSACTION_FEE:q,BANK_DISABLED:K,DEPOSIT_DISABLED:b}=X(),T=q*100;if(K)return e.jsx("div",{className:"mt-10 flex flex-col dark:text-slate-200",children:e.jsxs("div",{className:"mx-auto text-center",children:[e.jsx("h2",{className:"text-xl",children:"Bank currently Disabled"}),e.jsx("p",{children:"Please return later."})]})});b&&a==="Deposit"&&n("Withdraw");const Q=i=>{s(0),n(i.target.value)},U=i=>{s(Number.parseInt(i.target.value))},F=i=>{if(i.preventDefault(),a==="Deposit"){s(r?.cash??0);return}s(r?.bank_balance??0)},O=i=>{h(Number.parseInt(i.target.value))},R=i=>{i.preventDefault(),Y()};async function Y(){if(a==="Deposit"&&t<N){o("DepositAmountTooLow");return}if(a==="Withdraw"&&t<j){o("WithdrawAmountTooLow");return}if(a==="Transfer"){if(t<A){o("TransferAmountTooLow");return}if(r?.id===x){B.error("You can't bank transfer to yourself!");return}}try{a==="Deposit"?await l.mutateAsync({amount:t}):a==="Withdraw"?await y.mutateAsync({amount:t}):a==="Transfer"&&await g.mutateAsync({recipientId:x,transferAmount:t}),o(a),p(t),s(0)}catch(i){console.error(i),i instanceof Error&&i.message.includes("400")&&(a==="Deposit"?o("InvalidDepositAmount"):a==="Withdraw"&&o("InvalidWithdrawAmount")),B.error(i instanceof Error?i.message:"An unknown error occurred")}}return e.jsxs("div",{className:"mb-6 flex flex-col justify-center md:mx-auto md:mb-0 md:max-w-6xl",children:[b&&e.jsx(ae,{title:"Due to a recent security breach, all bank deposits are temporarily closed.",className:"mx-auto! w-fit! pr-4!"}),e.jsxs("div",{className:"mx-auto my-4 flex w-full flex-col border-y bg-white p-4 md:w-auto md:rounded-md md:border dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300",children:[e.jsxs("div",{className:"mx-auto",children:[e.jsxs("p",{children:["Current Bank Balance: ",d(r?.bank_balance||0)]}),e.jsxs("p",{children:["Current Cash Balance: ",d(r?.cash||0)]})]}),e.jsxs("div",{className:"text-center sm:flex-auto",children:[e.jsxs("p",{className:"mt-4 text-sm ",children:["You will be charged a ",T,"% transaction fee for all deposits and transfers."]}),e.jsx("p",{className:"mt-2 text-sm ",children:"Money in your bank cannot be mugged from you."})]}),e.jsxs("form",{className:"mx-auto mt-5 flex flex-col",onSubmit:R,children:[e.jsx("label",{htmlFor:"location",className:"block font-medium text-sm ",children:"Transaction Type"}),e.jsxs("select",{id:"location",name:"location",className:"mt-1 block w-full rounded-md border-gray-600 bg-gray-900 py-2 pr-10 pl-3 font-body font-semibold text-base focus:border-indigo-500 focus:outline-hidden focus:ring-indigo-500 sm:text-sm dark:text-gray-300",value:a,onChange:Q,children:[b?null:e.jsx("option",{className:"font-semibold",value:"Deposit",children:"Deposit"}),e.jsx("option",{className:"font-semibold",value:"Withdraw",children:"Withdraw"}),e.jsx("option",{className:"font-semibold",value:"Transfer",children:"Bank Transfer"})]}),e.jsxs("div",{className:"mt-3 mb-5",children:[e.jsx("label",{htmlFor:"amount",className:"block font-medium text-sm ",children:"Amount"}),e.jsxs("div",{className:"mt-1 flex gap-3 rounded-md shadow-xs",children:[e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"inline-flex items-center rounded-l-md border border-gray-600 border-r-0 bg-gray-900 px-3 text-gray-300 text-shadow sm:text-sm",children:J("yen")}),e.jsx("input",{type:"number",name:"amount",id:"amount",className:"block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-600 px-3 py-2 placeholder:text-gray-400 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-900 dark:text-gray-300",placeholder:"0",min:"0",value:t,onChange:U})]}),e.jsx(I,{variant:"flat",size:"md",onClick:F,children:"Max"})]})]}),a==="Transfer"&&e.jsxs("div",{className:"mt-3 mb-5",children:[e.jsx("label",{htmlFor:"amount",className:"block font-medium text-sm ",children:"Recipient Student ID"}),e.jsxs("div",{className:"mt-1 flex rounded-md shadow-xs",children:[e.jsx("span",{className:"inline-flex items-center rounded-l-md border border-gray-600 border-r-0 bg-gray-900 px-3 text-gray-300 text-shadow sm:text-sm",children:"#"}),e.jsx("input",{type:"number",name:"amount",id:"amount",className:"block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-600 px-3 py-2 placeholder:text-gray-400 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-900 dark:text-gray-300",placeholder:"0",onChange:O})]})]}),e.jsx(I,{fullWidth:!0,className:"mx-auto max-w-36 text-stroke-sm dark:text-slate-100",children:"Confirm"})]})]}),e.jsx("div",{className:"mb-2 h-16",children:u&&e.jsx(ne,{transactionType:u,amount:f,transferID:x,TRANSACTION_FEE:T,MINIMUM_WITHDRAWAL:j,MINIMUM_DEPOSIT:N,MINIMUM_TRANSFER:A})}),e.jsx(me,{historyLimit:$})]})}export{ge as default};
