import{u as B,j as e,o as x,e as j,g as w,c as d,l as m,v as O,r as U,P as y,c0 as c,S as G,D as p,aa as b,y as f,G as v}from"./index-UBkgY7aq.js";const P=({actionType:a,attacker:i})=>{const t=parseInt(i),{data:n}=B(t);let l;switch(a){case"Crippled by ":l="Crippled by ";break;case"Mugged by ":l="Mugged by ";break;case"Beaten up by ":l="Beaten up by ";break;default:l="Beaten up by "}return e.jsxs("span",{className:"text-gray-300 dark:text-stroke-sm",children:[l," ",t===0?e.jsx("span",{className:"",children:"Anonymous"}):e.jsx(x,{className:"text-blue-500",to:`/profile/${n?.id}`,children:n?.username})]})},q=({data:a})=>e.jsx("div",{className:"mt-auto hidden items-center justify-between border border-gray-200 bg-white px-4 py-3 sm:px-6 md:flex md:rounded-b-lg dark:border-gray-600 dark:bg-gray-800",children:e.jsx("div",{className:"text-shadow sm:flex sm:flex-1 sm:items-center sm:justify-between",children:e.jsx("div",{children:e.jsxs("p",{className:"text-gray-700 text-xs md:text-sm dark:text-gray-200",children:["Showing ",e.jsx("span",{className:"font-medium",children:"1"})," to"," ",e.jsx("span",{className:"font-medium",children:a?.length})," of"," ",e.jsx("span",{className:"font-medium",children:a?.length})," results"]})})})}),D=()=>{const a=j();return w(d.jail.bail.mutationOptions({onSuccess:()=>{m.success("Bailed out user successfully!"),a.invalidateQueries({queryKey:d.jail.jailList.key()}),a.invalidateQueries({queryKey:d.user.getCurrentUserInfo.key()})},onError:i=>{m.error(i?.message||"An error occurred")}}))},H=()=>{const a=j();return w(d.infirmary.revivePlayer.mutationOptions({onSuccess:()=>{m.success("Revived user successfully!"),a.invalidateQueries({queryKey:d.infirmary.getHospitalList.key()}),a.invalidateQueries({queryKey:d.infirmary.getInjuredList.key()})},onError:i=>{m.error(i?.message||"An error occurred")}}))},V=({data:a,isLoading:i,type:t,currentUser:n,reviveTalent:l,revivesRemaining:k,className:R})=>{const o=j(),E=D(),L=H(),h={faculty:{mobile:["LVL","TYPE","CLASS"],desktop:["LVL","TYPE","CLASS","GANG"]},class:{mobile:["LVL","POINTS"],desktop:["LVL","POINTS"]},hospital:{mobile:["REASON","LENGTH"],desktop:["REASON","LENGTH"]},jail:{mobile:["REASON","LENGTH","BAIL"],desktop:["REASON","LENGTH","BAIL"]},bounties:{mobile:["REWARD","REASON","PLACER"],desktop:["REWARD","REASON","PLACER"]}};t==="hospital"&&l&&(h.hospital.mobile.push("REVIVE"),h.hospital.desktop.push("REVIVE"));const S=O()?"mobile":"desktop",A=h[t][S],T=s=>{E.mutate({targetId:s})},I=s=>{L.mutate({targetId:s})};if(U.useEffect(()=>{t==="hospital"&&setTimeout(()=>o.invalidateQueries({queryKey:d.infirmary.getHospitalList.key()}),100),t==="jail"&&setTimeout(()=>o.invalidateQueries({queryKey:d.jail.jailList.key()}),100)},[t,o]),(!a||a?.length===0)&&!i){let s="";return t==="bounties"?s="The Bounty Board is currently empty.":s=`The ${y(t)} is currently empty.`,e.jsx("p",{className:"mt-5 text-center text-2xl dark:text-gray-200",children:s})}const C=(s,r)=>{if(!r)return"";const z=/(.*){{(.+?)}}/;if(s==="jail"&&r?.jailReason)switch(r?.jailReason){case"roguelike":return"Caught littering";case"scavenge":return"Caught scavenging";case"Assault":return"Assaulting another student";default:return r?.jailReason}if(s==="hospital"&&r?.hospitalisedReason){const u=r?.hospitalisedReason.match(z);let N,g;return u?(N=u[1],g=u[2]?.replace("{{","").replace("}}",""),/\d/.test(r?.hospitalisedReason)?e.jsx(P,{actionType:N,attacker:g}):`Beaten up by ${g} (Streets)`):r?.hospitalisedReason}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex flex-col",children:e.jsx("div",{className:"-my-2 sm:-mx-6 lg:-mx-8 overflow-x-auto",children:e.jsx("div",{className:"inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8",children:e.jsx("div",{className:c("overflow-hidden border-gray-200 border-t sm:rounded-t-lg dark:border-gray-600",R),children:i?e.jsx("div",{className:"flex size-full bg-white dark:bg-slate-800",children:e.jsx(G,{center:!0})}):e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 border-gray-200 text-shadow md:border-x dark:divide-gray-600 dark:border-gray-600 ",children:[e.jsx("thead",{className:"bg-gray-50 dark:bg-gray-800",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-[4.3rem] py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider md:px-20 dark:text-gray-200",children:t!=="bounties"?"Name":"Target"}),A.map((s,r)=>e.jsx("th",{scope:"col",className:"px-2 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider md:px-6 dark:text-gray-200",children:s},r))]})}),t!=="bounties"?e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white text-shadow dark:divide-gray-600 dark:bg-slate-800",children:a.map(s=>e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap px-3 py-4 md:px-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"size-10 shrink-0",children:e.jsx(p,{className:"size-10 rounded-full",src:s,loading:"lazy"})}),e.jsx(x,{to:`/profile/${s?.id}`,children:e.jsxs("div",{className:"ml-4",children:[e.jsxs("div",{className:"font-medium text-blue-600 text-sm text-stroke-sm md:text-base",children:[s?.username," ",e.jsxs("small",{className:"block text-gray-500 text-xs md:hidden dark:text-gray-400",children:["ID #",s?.id]})]}),e.jsxs("div",{className:"hidden text-gray-500 text-stroke-sm text-xs md:block md:text-sm dark:text-gray-400",children:["ID"," ",e.jsxs("span",{className:"text-indigo-400",children:["#",s?.id]})]}),t==="faculty"&&e.jsx("div",{className:"mt-1 block text-gray-700 text-xs md:hidden md:text-sm dark:text-gray-300 dark:text-stroke-sm",children:s?.gang===null?"No Gang":s?.gang.name})]})})]})}),t==="class"&&e.jsxs(e.Fragment,{children:[e.jsx("td",{className:"whitespace-nowrap px-2 py-4 md:px-6",children:e.jsx("div",{className:"text-gray-900 text-xs md:text-sm dark:text-custom-yellow dark:text-stroke-sm",children:s?.level||1})}),e.jsx("td",{className:"whitespace-nowrap px-2 py-4 md:px-6",children:e.jsx("div",{className:"text-gray-900 text-xs md:text-sm dark:text-custom-yellow dark:text-stroke-sm",children:s?.classPoints||0})})]}),t==="faculty"?e.jsxs(e.Fragment,{children:[e.jsx("td",{className:"whitespace-nowrap px-2 py-4 md:px-6",children:e.jsx("div",{className:"text-gray-900 text-xs md:text-sm dark:text-custom-yellow dark:text-stroke-sm",children:s.level})}),e.jsx("td",{className:"whitespace-nowrap px-2 py-4 md:px-6",children:e.jsx("div",{className:c(s?.userType==="admin"?"text-red-600":"text-green-600","text-xs md:text-sm dark:text-stroke-sm"),children:s.userType==="admin"?"Staff":y(s.userType)})}),e.jsx("td",{className:"whitespace-nowrap px-1 py-4 md:px-4",children:e.jsx("span",{className:"inline-flex px-2 text-green-600 text-stroke-sm text-xs leading-5 md:text-sm dark:text-gray-200",children:y(s.class)})}),e.jsx("td",{className:"hidden whitespace-nowrap px-6 py-4 text-gray-500 text-xs md:table-cell md:text-sm dark:text-gray-200 dark:text-stroke-sm",children:s.gang===null?"No Gang":s.gang.name})]}):e.jsx(e.Fragment,{children:t!=="class"&&e.jsxs(e.Fragment,{children:[" ",e.jsx("td",{className:"whitespace-nowrap px-3 py-4 md:px-6",children:e.jsx("div",{className:"text-gray-900 text-xs md:text-sm dark:text-gray-200",children:C(t,s)})}),e.jsx("td",{className:"whitespace-nowrap px-5 py-4 md:px-6",children:e.jsx("span",{className:"inline-flex rounded-full bg-green-100 px-2 font-semibold text-green-800 text-xs leading-5 dark:bg-blue-700 dark:font-normal dark:text-custom-yellow dark:text-stroke-s-sm dark:ring-1 dark:ring-black",children:t==="jail"?b(s.jailedUntil):b(s.hospitalisedUntil)})})]})}),t==="jail"&&e.jsxs("td",{className:"whitespace-nowrap px-5 py-4 text-stroke-sm md:px-6 dark:text-gray-300",children:[e.jsx("span",{className:"inline-flex rounded-full px-2 font-semibold text-sm leading-5 dark:font-normal dark:text-amber-500",children:f(s.level*300)}),e.jsx(v,{size:"sm",variant:"flat",className:"block mt-1.5",disabled:n?.cash<s.level*300,onClick:()=>T(s.id),children:"Bail"})]}),t==="hospital"&&l&&e.jsx("td",{className:"whitespace-nowrap px-5 py-4 text-stroke-sm md:px-6 dark:text-gray-300",children:e.jsxs(v,{size:"sm",variant:"flat",className:"block",disabled:k===0,onClick:()=>I(s.id),children:["Revive (",k,")"]})})]},s?.id))}):e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white text-shadow dark:divide-gray-600 dark:bg-slate-800",children:a.map(s=>e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap px-3 py-4 md:px-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"size-10 shrink-0",children:e.jsx(p,{className:"size-10 rounded-full",src:s?.target,loading:"lazy"})}),e.jsx(x,{to:`/profile/${s?.target?.id}`,children:e.jsxs("div",{className:"ml-4",children:[e.jsxs("div",{className:"font-medium text-blue-600 text-sm text-stroke-sm md:text-base",children:[s?.target?.username," ",e.jsxs("small",{className:"block text-gray-500 text-xs md:hidden dark:text-gray-300",children:["Student #",s?.target?.id]})]}),e.jsxs("div",{className:"hidden text-gray-500 text-stroke-sm text-xs md:block md:text-sm dark:text-gray-300",children:["Student"," ",e.jsxs("span",{className:"text-indigo-400",children:["#",s?.target?.id]})]})]})})]})}),e.jsx("td",{className:"whitespace-nowrap px-5 py-4 md:px-6",children:e.jsx("span",{className:"inline-flex rounded-full bg-green-100 px-2 font-semibold text-green-800 text-xs leading-5 dark:bg-blue-700 dark:font-normal dark:text-custom-yellow dark:text-stroke-s-sm dark:ring-1 dark:ring-black",children:f(s.amount)})}),e.jsx("td",{className:"whitespace-nowrap px-3 py-4 md:px-6",children:e.jsx("div",{className:"text-gray-900 text-xs md:text-sm dark:text-gray-200",children:s.reason})}),e.jsx("td",{className:"whitespace-nowrap px-3 py-4 md:px-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"size-10 shrink-0",children:e.jsx(p,{className:"size-10 rounded-full",src:s?.placer,loading:"lazy"})}),e.jsx(x,{to:`/profile/${s?.placer?.id}`,children:e.jsxs("div",{className:"ml-4",children:[e.jsxs("div",{className:"font-medium text-blue-600 text-sm text-stroke-sm md:text-base",children:[s?.placer?.username," ",e.jsx("small",{className:c(s?.placerId===5?"text-red-500":"text-gray-300","block text-xs md:hidden"),children:s?.placerId===5?"Disciplinary Overseer":e.jsxs(e.Fragment,{children:["Student"," ",e.jsxs("span",{className:"text-indigo-400",children:["#",s?.placer?.id]})]})})]}),e.jsx("div",{className:c(s?.placerId===5?"text-red-500":"text-gray-300","hidden text-stroke-sm text-xs md:block md:text-sm"),children:s?.placerId===5?"Disciplinary Overseer":e.jsxs(e.Fragment,{children:["Student"," ",e.jsxs("span",{className:"text-indigo-400",children:["#",s?.placer?.id]})]})})]})})]})})]},s?.id))})]})})})})}),e.jsx(q,{data:a})]})};export{V as U};
