import { cn } from "@/lib/utils";
import { ReactNode } from "react";
import { Portal } from "react-portal";

interface FullscreenModalProps {
    children: ReactNode;
    onClick: () => void;
    color?: string;
}

const FullscreenModal = ({ children, onClick, color = "bg-[#00ADFE]" }: FullscreenModalProps) => {
    return (
        <Portal>
            <div className={cn(color, "fixed inset-0 z-999 h-screen w-screen brightness-90")} onClick={() => onClick()}>
                <div className="absolute inset-0 h-72 w-full bg-linear-to-b from-violet-400/75 to-transparent mix-blend-hue"></div>
                <div className="gradientBackgroundFull absolute inset-0 z-[-1] size-full opacity-10"></div>

                {children}
            </div>
        </Portal>
    );
};

export default FullscreenModal;
