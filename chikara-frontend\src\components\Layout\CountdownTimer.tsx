import { api } from "@/helpers/api";
import { useQueryClient } from "@tanstack/react-query";
import { isValid } from "date-fns";
import Countdown from "react-countdown";
import { ReactElement } from "react";

interface CountdownTimerProps {
    targetDate: Date | number;
    showHours?: boolean;
    showSeconds?: boolean;
    showHoursText?: boolean;
}

interface RendererProps {
    formatted: {
        hours: string;
        minutes: string;
        seconds: string;
    };
    completed: boolean;
}

export const CountdownTimer = ({
    targetDate,
    showHours,
    showSeconds = true,
    showHoursText,
}: CountdownTimerProps): ReactElement | null => {
    const queryClient = useQueryClient();

    const isValidDate = isValid(targetDate);
    if (!isValidDate) return null;

    const renderer = ({ formatted: { hours, minutes, seconds }, completed }: RendererProps): ReactElement | null => {
        if (completed) {
            return null;
        } else {
            return (
                <span className="text-white font-medium font-display">
                    {showHours && hours + ":"}
                    {minutes}
                    {showSeconds && ":" + seconds}
                    {showHoursText && "hrs"}
                </span>
            );
        }
    };

    // Convert targetDate to number if it's a Date object
    const dateValue = targetDate instanceof Date ? targetDate.getTime() : targetDate;

    return (
        <Countdown
            key={dateValue}
            data-testid="countdown-timer"
            date={dateValue + 1000}
            renderer={renderer}
            onComplete={() =>
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                })
            }
        />
    );
};
