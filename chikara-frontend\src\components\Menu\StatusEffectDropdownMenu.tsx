import StatusEffects from "@/components/StatusEffects";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { HeartCrack, ChevronDown } from "lucide-react";
import { User } from "@/types/user";
import { cn } from "@/lib/utils";
import type { StatusEffect } from "@/hooks/api/useGetStatusEffects";

interface StatusEffectDropdownMenuProps {
    currentUser: User;
    statusEffects: StatusEffect[];
}

export default function StatusEffectDropdownMenu({ currentUser, statusEffects }: StatusEffectDropdownMenuProps) {
    return (
        <DropdownMenu>
            <DropdownMenuTrigger
                asChild
                className="rounded-lg border-blue-500 data-[state=open]:text-blue-500 data-[state=open]:ring-3"
            >
                <button
                    className={cn(
                        "-bottom-12 absolute flex rounded-md border border-gray-500 bg-black/85 pr-0.5 pl-2 text-base text-red-500 text-stroke-sm shadow-xl",
                        currentUser?.hospitalisedUntil || currentUser?.jailedUntil || currentUser?.missionEnds
                            ? "-left-2"
                            : "left-16"
                    )}
                >
                    <HeartCrack className="my-auto mr-1.5 size-4 text-red-600" />
                    <p className="text-red-400">
                        {statusEffects.length} Injur{statusEffects.length > 1 ? "ies" : "y"}
                    </p>

                    <ChevronDown
                        className={cn(
                            "my-auto ml-1 size-5 scale-125 text-gray-400 transition-transform duration-200",
                            "data-[state=open]:rotate-180"
                        )}
                    />
                </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="!dark">
                <DropdownMenuGroup>
                    <DropdownMenuItem className="p-0!" onClick={(e) => e.preventDefault()}>
                        <div className="relative flex h-fit w-full flex-col rounded-lg border-2 border-gray-200 bg-white pt-1.5 pb-1 dark:border-red-500 dark:bg-gray-800">
                            <StatusEffects currentEffects={statusEffects} className="gap-4" />
                        </div>
                    </DropdownMenuItem>
                </DropdownMenuGroup>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
