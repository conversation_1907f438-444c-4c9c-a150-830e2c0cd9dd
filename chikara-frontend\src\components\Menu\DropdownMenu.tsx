import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    chatBanUser,
    deleteMessage,
    hideMessage,
    timeoutUser,
    unhideMessage,
    useHideMessage,
    useUnhideMessage,
    useDeleteMessage,
    useChatBanUser,
} from "@/features/chat/helpers/chatHelpers";
import { AlertTriangle, Ban, Clock, Eye, EyeOff, MoreVertical, Reply, Trash2, User } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { ChatMessage } from "@/features/chat/types/chat";
import { UserType } from "@/types/user";

interface ChatDropdownMenuProps {
    userType: UserType;
    msg: ChatMessage;
    setSelectedReplyMessage: (msg: ChatMessage) => void;
    setFocusChatMsgInput: (focus: boolean) => void;
}

export function ChatDropdownMenu({ userType, msg, setSelectedReplyMessage, setFocusChatMsgInput }: ChatDropdownMenuProps) {
    const navigate = useNavigate();

    const handleReply = () => {
        setSelectedReplyMessage(msg);
        setFocusChatMsgInput(true);
    };

    return (
        <DropdownMenu className="dark">
            <DropdownMenuTrigger
                asChild
                className="rounded-lg border-blue-500 data-[state=open]:text-blue-500 data-[state=open]:ring-3"
            >
                <button className="py-1" variant="outline">
                    <MoreVertical className="size-4 cursor-pointer" />
                </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="dark w-56">
                {(userType === "admin" || userType === "prefect") && msg?.user?.userType !== "admin" ? (
                    <ModeratorDropdownOptions msg={msg} />
                ) : null}
                {userType === "admin" ? <AdminDropdownOptions msg={msg} /> : null}
                <DropdownMenuGroup>
                    <DropdownMenuItem>
                        <div className="flex cursor-default opacity-50">
                            <AlertTriangle className="my-auto mr-2 size-4" />
                            Report
                        </div>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                        onClick={() => {
                            navigate("/profile/" + msg?.userId);
                        }}
                    >
                        <User className="mr-2 size-4" />
                        Profile
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleReply()}>
                        <Reply className="mr-2 size-4" />
                        Reply
                    </DropdownMenuItem>
                </DropdownMenuGroup>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}

interface DropdownOptionsProps {
    msg: ChatMessage;
}

const AdminDropdownOptions = ({ msg }: DropdownOptionsProps) => {
    const deleteMessageMutation = useDeleteMessage();
    const chatBanMutation = useChatBanUser();

    return (
        <>
            <DropdownMenuGroup>
                <DropdownMenuItem onSelect={() => deleteMessage(deleteMessageMutation, msg.id)}>
                    <Trash2 className="mr-2 size-4" />
                    Delete Message
                </DropdownMenuItem>
                <DropdownMenuItem onSelect={() => chatBanUser(chatBanMutation, msg)}>
                    <Ban className="mr-2 size-4" />
                    Ban User
                </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
        </>
    );
};

const ModeratorDropdownOptions = ({ msg }: DropdownOptionsProps) => {
    const hideMessageMutation = useHideMessage();
    const unhideMessageMutation = useUnhideMessage();
    const chatBanMutation = useChatBanUser();

    return (
        <>
            <DropdownMenuLabel className="font-bold font-body">Moderation</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
                {msg?.hidden ? (
                    <DropdownMenuItem onSelect={() => unhideMessage(unhideMessageMutation, msg.id)}>
                        <Eye className="mr-2 size-4" />
                        Unhide Message
                    </DropdownMenuItem>
                ) : (
                    <DropdownMenuItem onSelect={() => hideMessage(hideMessageMutation, msg.id)}>
                        <EyeOff className="mr-2 size-4" />
                        Hide Message
                    </DropdownMenuItem>
                )}
                <DropdownMenuItem onSelect={() => timeoutUser(chatBanMutation, msg)}>
                    <Clock className="mr-2 size-4" />
                    20m Timeout
                </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
        </>
    );
};
