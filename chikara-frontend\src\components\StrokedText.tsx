import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface StrokedTextProps {
    children?: ReactNode;
    text?: string | null;
    className?: string;
    hideShadow?: boolean;
}

const StrokedText = ({ children, text = null, className = "", hideShadow = false }: StrokedTextProps): ReactNode => {
    if (children && typeof children !== "string") {
        return children;
    }
    return (
        <span className={cn("relative z-10", className)}>
            <span data-text={children || text} className={cn("stroke-sm", hideShadow && "text-stroke-0!")}>
                {children || text}
            </span>
        </span>
    );
};

export default StrokedText;
