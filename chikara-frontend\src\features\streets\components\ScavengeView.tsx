import { DisplayItem } from "@/components/DisplayItem";
import StatusEffects from "@/components/StatusEffects";
import { api } from "@/helpers/api";
import { rarityColours } from "@/helpers/rarityColours";
import { getScavengeLocation } from "@/helpers/scavengeLocations";
import { sceneManager } from "@/helpers/sceneManager";
import { cn } from "@/lib/utils";
import { useQueryClient } from "@tanstack/react-query";
import { motion, useAnimation } from "framer-motion";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useScavengeOption } from "../api/useScavengeOption";
import { type User } from "@/types/user";
import { type Item } from "@/types/item";

export default function ScavengeView({ currentMap }: { currentMap: User["roguelikeMap"] }) {
    const queryClient = useQueryClient();
    const [scavengeResult, setScavengeResult] = useState(null);
    const controls = useAnimation();

    const scavengeOption = useScavengeOption(setScavengeResult);

    const scavengeChoices = currentMap?.currentNodeChoices;

    // const scavengeChoices = ["trash", "medical", "upgrade", "herb", "tech", "ore"];
    // // get 2 random choices
    // const scavengeChoice1 = scavengeChoices[Math.floor(Math.random() * scavengeChoices.length)];
    // const scavengeChoice2 = scavengeChoices[Math.floor(Math.random() * scavengeChoices.length)];

    const locationDetails = getScavengeLocation(scavengeChoices);

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { duration: 1 } },
    };

    const wipeVariants = {
        hidden: { width: "100%", left: 0 },
        visible: {
            width: 0,
            left: "50%",
            transition: { ease: "linear", duration: 0.75 },
        },
    };

    const dialogueBoxVariants = {
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { duration: 0.1 } },
    };

    useEffect(() => {
        controls.start("visible");
    }, []);

    const chooseOption = (choice: { choice: number }) => {
        scavengeOption.mutate(choice);
    };

    const returnToStreets = async () => {
        await queryClient.invalidateQueries({
            queryKey: api.roguelike.getCurrentMap.key(),
        });
    };

    const displayActionText = (scavengeType: string) => {
        return locationDetails?.choices?.[scavengeType]?.action;
    };

    const displayItemTypeIcon = (scavengeType: string) => {
        switch (scavengeType) {
            case "trash":
                return "https://img.icons8.com/?size=100&id=67367&format=png";
            case "medical":
                return "https://img.icons8.com/?size=100&id=vB6PoShzdZKw&format=png";
            case "upgrade":
                return "https://img.icons8.com/?size=100&id=HA930KjFD9ki&format=png";
            case "herb":
                return "https://img.icons8.com/?size=100&id=ecZ41Q9R6SIW&format=png";
            case "tech":
                return "https://img.icons8.com/?size=100&id=fzdr7mjSaS_w&format=png";
            case "ore":
                return "https://img.icons8.com/?size=100&id=Iqzd6gV1vpm5&format=png";
            default:
                return "";
        }
    };
    // const backgrounds = ["highstreet1", "highstreet2", "citystreet1", "shoppingstreet2"];
    // const selectedBG = backgrounds[Math.floor(Math.random() * backgrounds.length)];
    const failureType = scavengeResult?.injury ? "failureInjury" : "failureJail";

    return (
        <motion.div className="shadow md:rounded-b-lg" variants={containerVariants} initial="hidden" animate={controls}>
            <motion.div className="wipe" variants={wipeVariants} initial="hidden" animate="visible" />
            <div className="relative h-full overflow-hidden md:h-full md:rounded-lg">
                <img
                    className="h-full object-cover md:h-full md:scale-105 md:rounded-lg"
                    src={sceneManager("citystreet1")}
                    alt=""
                />
                <div className="absolute bottom-0 size-full bg-black opacity-20 md:rounded-b-lg"></div>
                <motion.div variants={dialogueBoxVariants} initial="hidden" animate="visible">
                    <div
                        className={cn(
                            "-translate-x-1/2 -translate-y-1/2 vignette-sm absolute top-1/2 left-1/2 z-50 w-[90%] justify-end rounded-2xl border-4 border-indigo-500 bg-slate-800 pb-2 text-white shadow-lg md:w-3/4"
                        )}
                    >
                        <div className="mx-6 mb-2 flex h-full flex-col">
                            <div className="mt-5 flex flex-col items-center justify-center md:mx-8 md:text-2xl">
                                <p className="mb-1 inline-block text-indigo-400 text-lg lg:text-2xl">
                                    {locationDetails?.location}
                                </p>
                            </div>
                            {scavengeResult ? (
                                <>
                                    {scavengeResult?.success ? (
                                        <ScavengeSuccessPanel
                                            scavengeResult={scavengeResult}
                                            successText={locationDetails?.choices?.[scavengeResult?.choice]?.success}
                                            returnToStreets={returnToStreets}
                                        />
                                    ) : (
                                        <ScavengeFailurePanel
                                            scavengeResult={scavengeResult}
                                            returnToStreets={returnToStreets}
                                            failureText={
                                                locationDetails?.choices?.[scavengeResult?.choice]?.[failureType]
                                            }
                                        />
                                    )}
                                </>
                            ) : (
                                <>
                                    <p className="prose-sm lg:prose-base 2xl:prose-lg inline-block text-center font-body text-gray-200">
                                        {locationDetails?.description}
                                    </p>
                                    <div className="my-4 flex flex-col gap-5 lg:gap-2.5 2xl:gap-5">
                                        <button
                                            type="button"
                                            className="darkBlueButtonBGSVG mx-auto flex h-20 w-4/5 max-w-152 items-center justify-center rounded-xl font-lili text-sm text-stroke-s-sm uppercase shadow-xs md:mt-3 lg:h-[4.4rem] lg:text-lg 2xl:h-20 dark:text-slate-200"
                                            onClick={() => chooseOption({ choice: 1 })}
                                        >
                                            <div className="-ml-1 flex w-full items-center justify-center px-5">
                                                <img
                                                    src={displayItemTypeIcon(scavengeChoices?.[0])}
                                                    className="mr-1 h-8 w-auto md:h-12"
                                                    alt=""
                                                />
                                                <span className="text-sm lg:text-base 2xl:text-lg">
                                                    {displayActionText(scavengeChoices?.[0])}
                                                </span>
                                            </div>
                                        </button>

                                        <button
                                            type="button"
                                            className="darkBlueButtonBGSVG mx-auto h-20 w-4/5 max-w-152 rounded-xl font-lili text-sm text-stroke-s-sm uppercase shadow-xs md:mt-3 lg:h-[4.4rem] lg:text-lg 2xl:h-20 dark:text-slate-200"
                                            onClick={() => chooseOption({ choice: 2 })}
                                        >
                                            <div className="-ml-1 flex w-full items-center justify-center px-5">
                                                <img
                                                    src={displayItemTypeIcon(scavengeChoices?.[1])}
                                                    className="mr-1 h-8 w-auto md:h-12"
                                                    alt=""
                                                />
                                                <span className="text-sm lg:text-base 2xl:text-lg">
                                                    {displayActionText(scavengeChoices?.[1])}
                                                </span>
                                            </div>
                                        </button>
                                    </div>
                                </>
                            )}
                        </div>
                    </div>
                </motion.div>
            </div>
        </motion.div>
    );
}

interface ScavengeResult {
    success: boolean;
    choice: number;
    itemQuantity: number;
    itemReward?: Item;
    injury: string;
    jailed: boolean;
}

const ScavengeSuccessPanel = ({
    scavengeResult,
    successText,
    returnToStreets,
}: {
    scavengeResult: ScavengeResult;
    successText: string;
    returnToStreets: () => void;
}) => {
    return (
        <div className="flex h-full flex-col items-center gap-2 px-4 py-1 text-base lg:px-8 lg:text-lg">
            <p className="prose-sm lg:prose-base 2xl:prose-lg font-body">{successText}</p>
            <div className="my-auto flex items-center gap-2">
                <p>{scavengeResult?.itemQuantity}x </p>
                <DisplayItem item={scavengeResult?.itemReward} height="h-8 w-auto" className="inline-block" />
                <p className={cn(rarityColours(scavengeResult?.itemReward?.rarity || "common"))}>
                    {scavengeResult?.itemReward?.name}
                </p>
            </div>
            <button
                className="mt-auto! mx-2 mb-[0.35rem] min-w-28 rounded-md border-2 border-black bg-indigo-600 px-3 py-2 font-medium text-sm text-stroke-sm text-white shadow-xl hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:mt-0 md:px-4 lg:px-8 lg:py-2.5 lg:text-base"
                onClick={() => {
                    returnToStreets();
                }}
            >
                Return to streets
            </button>
        </div>
    );
};

const ScavengeFailurePanel = ({
    scavengeResult,
    failureText,
    returnToStreets,
}: {
    scavengeResult: ScavengeResult;
    failureText: string;
    returnToStreets: () => void;
}) => {
    const navigate = useNavigate();
    return (
        <div className="flex h-full flex-col items-center gap-2 px-4 py-1">
            <p className="prose-sm lg:prose-base 2xl:prose-lg text-center font-body">{failureText}</p>
            {scavengeResult?.injury && (
                <StatusEffects
                    currentEffects={[{ id: 1, count: 1, debuff: scavengeResult?.injury }]}
                    className="mb-2"
                />
            )}
            {scavengeResult?.jailed ? (
                <button
                    className="mt-auto! mx-2 mb-[0.35rem] min-w-28 rounded-md border-2 border-black bg-indigo-600 px-3 py-2 font-medium text-sm text-stroke-sm text-white shadow-xl hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:mt-0 md:px-4 lg:px-8 lg:py-2.5 lg:text-base"
                    onClick={() => {
                        navigate("/jail");
                    }}
                >
                    To Jail
                </button>
            ) : (
                <button
                    className="mt-auto! mx-2 mb-[0.35rem] min-w-28 rounded-md border-2 border-black bg-indigo-600 px-3 py-2 font-medium text-sm text-stroke-sm text-white shadow-xl hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:mt-0 md:px-4 lg:px-8 lg:py-2.5 lg:text-base"
                    onClick={() => {
                        returnToStreets();
                    }}
                >
                    Return to streets
                </button>
            )}
        </div>
    );
};
