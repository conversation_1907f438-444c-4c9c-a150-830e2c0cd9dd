import NotificationBadge from "@/components/NotificationBadge";
import { cn } from "@/lib/utils";
import { Link } from "react-router-dom";
import { useNormalStore } from "../../app/store/stores";

interface NavIconProps {
    name: string;
    img: string;
    href?: string;
    onClick?: () => void;
    notifications?: number | false;
    notifSize?: "lg" | "sm";
    disabled?: boolean;
    pulse?: boolean;
    external?: boolean;
}

export default function NavIcon({
    name,
    img,
    href,
    onClick,
    notifications,
    notifSize = "lg",
    disabled,
    pulse = false,
    external = false,
}: NavIconProps) {
    const { preventNavigation } = useNormalStore();
    let isDisabled = disabled;
    if (preventNavigation) {
        isDisabled = true;
    }
    const iconClassName = cn(
        "mt-0.5 h-10 w-auto cursor-pointer rounded-xl border border-slate-700 border-transparent bg-indigo-600 object-cover p-1.5 shadow-lg hover:border-slate-600 2xl:h-14 2xl:p-2 dark:bg-slate-800",
        isDisabled && "opacity-50"
    );
    if (onClick) return <img className={iconClassName} src={img} alt={name} onClick={onClick} />;

    return (
        <Link
            data-tooltip-id="default-tooltip"
            data-tooltip-content={name}
            className="relative h-10 w-auto 2xl:h-14"
            to={href}
            target={external ? "_blank" : null}
        >
            <img className={iconClassName} src={img} alt="" />
            {notifications && notifications > 0 && !isDisabled ? (
                <NotificationBadge pulse={pulse} notifSize={notifSize} amount={notifications} />
            ) : null}
        </Link>
    );
}
