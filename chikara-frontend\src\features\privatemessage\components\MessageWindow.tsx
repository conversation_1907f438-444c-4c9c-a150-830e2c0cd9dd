import { DisplayAvatar } from "@/components/DisplayAvatar";
import LoadingState from "@/components/LoadingState";
import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { cn } from "@/lib/utils";
import { Menu, Transition } from "@headlessui/react";
import { ArrowLeft, MoreVertical } from "lucide-react";
import { Fragment } from "react";
import { Link, useNavigate } from "react-router-dom";
import PrivateMessage from "./PrivateMessage";
import SendMessageForm from "./SendMessageForm";
import { MessageWindowProps, MessageDropdownMenuProps } from "../types/privateMessage";

export default function MessageWindow({ sortedArray, sentMessages, currentUser, convoId }: MessageWindowProps) {
    const { data: convoUserInfo, isLoading: senderLoading } = useGetUserInfo(convoId ? Number.parseInt(convoId) : 0, {
        enabled: !!convoId,
    });
    const navigate = useNavigate();

    const safeSentMessages = sentMessages || [];
    const currentSentMessages = safeSentMessages.filter(
        (el) => el.receiverId === Number.parseInt(convoUserInfo?.id?.toString() || "0")
    );

    const currentReceievedMessages = sortedArray.filter((el) => el.senderId === convoId);
    const combinedMessages = currentSentMessages.concat(currentReceievedMessages[0]?.messages || []);
    combinedMessages.sort((a, b) => (a.createdAt < b.createdAt ? 1 : -1));

    return (
        <section aria-labelledby="message-heading" className="flex h-full min-w-0 flex-1 flex-col overflow-hidden">
            {!convoId ? (
                <div className="flex size-full">
                    <div className="m-auto text-2xl text-gray-300">Select a message</div>
                </div>
            ) : (
                <LoadingState size={14} isLoading={senderLoading}>
                    <div className="relative flex min-h-0 flex-1 flex-col">
                        <div className="bg-gray-800 pt-2 pb-3 shadow-sm">
                            <div className="flex items-baseline px-4 sm:px-6 lg:px-8">
                                <ArrowLeft
                                    className="my-auto mr-5 size-7 cursor-pointer md:hidden text-gray-200"
                                    onClick={() => navigate("/inbox")}
                                />
                                <Link to={`/profile/${convoUserInfo?.id}`}>
                                    <div className="my-auto mr-3">
                                        <DisplayAvatar className="w-10 rounded-full md:w-14" src={convoUserInfo} />
                                    </div>
                                </Link>
                                <div className="my-auto sm:w-0 sm:flex-1">
                                    <Link to={`/profile/${convoUserInfo?.id}`}>
                                        <h1
                                            id="message-heading"
                                            className="font-medium text-custom-yellow text-sm md:text-lg"
                                        >
                                            {convoUserInfo?.username}{" "}
                                            <span className="text-gray-400 text-sm">#{convoUserInfo?.id}</span>
                                        </h1>
                                    </Link>
                                    <p className="mt-1 truncate text-red-400 text-xs">
                                        {convoUserInfo?.gang?.name || "No Gang"}
                                    </p>
                                </div>

                                <MessageDropdownMenu convoUserInfo={convoUserInfo} />
                            </div>
                        </div>
                        {/* Thread section*/}
                        <ul className="flex flex-1 flex-col-reverse gap-4 overflow-y-auto p-4 pb-16 font-body md:pb-14 lg:px-8">
                            {combinedMessages.map((message) =>
                                message?.senderId === currentUser?.id ? (
                                    <PrivateMessage
                                        key={message?.id}
                                        type="sentMessage"
                                        message={message}
                                        convoUserInfo={convoUserInfo}
                                        currentUser={currentUser}
                                    />
                                ) : (
                                    <PrivateMessage
                                        key={message?.id}
                                        type="receivedMessage"
                                        message={message}
                                        convoUserInfo={convoUserInfo}
                                        currentUser={currentUser}
                                    />
                                )
                            )}
                        </ul>
                        <div className="h-10 w-full border-gray-600 border-t bg-gray-700 ">
                            <SendMessageForm userId={convoUserInfo?.id} currentUser={currentUser} />
                        </div>
                    </div>
                </LoadingState>
            )}
        </section>
    );
}

const MessageDropdownMenu = ({ convoUserInfo }: MessageDropdownMenuProps) => {
    if (!convoUserInfo) return null;
    return (
        <Menu as="div" className="ml-3 inline-block text-left">
            <div>
                <Menu.Button className="absolute top-3 right-5 rounded-full p-2 text-gray-400 hover:text-gray-600 focus:outline-hidden focus:ring-2 focus:ring-blue-600 md:top-5">
                    <span className="sr-only">Open options</span>
                    <MoreVertical className="size-5" aria-hidden="true" />
                </Menu.Button>
            </div>

            <Transition
                as={Fragment}
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
            >
                <Menu.Items className="absolute right-0 z-150 mt-2 w-56 origin-top-right rounded-md border border-gray-400 bg-gray-900 shadow-lg ring-1 ring-black/5 focus:outline-hidden">
                    <div className="py-1">
                        <Menu.Item>
                            {({ active }) => (
                                <button
                                    type="button"
                                    className={cn(
                                        active ? "bg-gray-700 text-gray-200" : "text-gray-200",
                                        "flex w-full justify-between px-4 py-2 text-sm"
                                    )}
                                >
                                    <span>Block {convoUserInfo?.username}</span>
                                </button>
                            )}
                        </Menu.Item>
                        <Menu.Item>
                            {({ active }) => (
                                <a
                                    href="#"
                                    className={cn(
                                        active ? "bg-gray-700 text-gray-200" : "text-gray-200",
                                        "flex justify-between px-4 py-2 text-sm"
                                    )}
                                >
                                    <span>Report {convoUserInfo?.username}</span>
                                </a>
                            )}
                        </Menu.Item>
                        <Menu.Item>
                            {({ active }) => (
                                <a
                                    href="#"
                                    className={cn(
                                        active ? "bg-gray-700 text-gray-200" : "text-gray-200",
                                        "flex justify-between px-4 py-2 text-sm"
                                    )}
                                >
                                    <span>Delete conversation</span>
                                </a>
                            )}
                        </Menu.Item>
                    </div>
                </Menu.Items>
            </Transition>
        </Menu>
    );
};
