import{au as w,r as z,j as t,d as g}from"./index-UBkgY7aq.js";import{A as j}from"./ag-theme-quartz-BkmUHXm0.js";const S=({currentTab:l,setCurrentTab:i,tabs:a})=>t.jsx("div",{className:"flex w-full divide-x-2 divide-slate-600 rounded-lg border-slate-600 border-x border-t",children:a.map((e,s)=>t.jsxs("button",{"aria-current":e.current?"page":void 0,className:g(e.current?"text-gray-900":"text-gray-500",s===0?"rounded-tl-lg":"rounded-tr-lg",(s===a.length-1,""),"group relative min-w-0 flex-1 overflow-hidden bg-white px-4 py-3 text-center font-medium text-lg focus:z-10 dark:bg-slate-800 dark:text-white"),onClick:()=>{i(e.value)},children:[t.jsx("span",{className:"text-stroke-sm",children:e.name}),t.jsx("span",{"aria-hidden":"true",className:g(e.current?"bg-indigo-600":"bg-slate-700","absolute inset-x-0 bottom-0 h-[0.15rem]")})]},e.name))}),N=({dataList:l,colDefs:i,initialFilter:a,isLoading:e,rowHeight:s=80,currentTab:c,setCurrentTab:p,tabs:u,customGridRef:x,keyProp:m=void 0,rowBuffer:f=20})=>{const{persistedTablePageSize:o,setPersistedTablePageSize:h}=w(),b=z.useRef(null),P={flex:1,sortable:!0,filter:!0,resizable:!0,cellClass:"px-1.5! md:px-2! 2xl:px-6!",floatingFilter:!0,suppressHeaderMenuButton:!0,suppressMovable:!0,filterParams:{maxNumConditions:1}},d=[10,30,50],v=r=>{if(o&&r.api&&r.newPageSize===!0){const n=r.api.paginationGetPageSize();d.includes(n)&&n!==o&&h(n)}};return t.jsxs("div",{className:"ag-theme-quartz-dark rounded-t-lg 2xl:p-2",style:{width:"100%",overflow:"auto"},children:[u&&t.jsx(S,{setCurrentTab:p,currentTab:c,tabs:u}),t.jsx(j,{ref:x||b,suppressCellFocus:!0,suppressRowHoverHighlight:!0,pagination:!0,rowBuffer:f,rowData:l,columnDefs:i,defaultColDef:P,domLayout:"autoHeight",rowHeight:s,paginationPageSizeSelector:d,paginationPageSize:o||10,onPaginationChanged:r=>v(r),onFirstDataRendered:a},m)]})};export{N as D};
