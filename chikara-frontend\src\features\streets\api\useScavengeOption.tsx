import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { type AppRouterClient } from "@/lib/orpc";

type ScavengeResult = Awaited<ReturnType<AppRouterClient["roguelike"]["chooseScavengeOption"]>>;

export const useScavengeOption = (setScavengeResult?: (_data: ScavengeResult) => void) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.roguelike.chooseScavengeOption.mutationOptions({
            onSuccess: (data) => {
                // Update scavenge result if callback provided
                if (setScavengeResult) {
                    setScavengeResult(data);
                }

                // Invalidate current map to get updated state
                queryClient.invalidateQueries({
                    queryKey: api.roguelike.getCurrentMap.key(),
                });

                // Invalidate user info to update inventory/stats
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
            },
            onError: (error) => {
                console.error("Scavenge option error:", error);
                toast.error(error.message || "Failed to choose scavenge option");
            },
        })
    );
};
