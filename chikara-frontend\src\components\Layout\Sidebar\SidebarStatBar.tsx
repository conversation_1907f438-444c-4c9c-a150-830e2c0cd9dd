import apImg from "@/assets/icons/UI/APicon3.png";
import hpImg from "@/assets/icons/UI/HPicon.png";
import energyImg from "@/assets/icons/UI/energyicon.png";
import expImg from "@/assets/icons/UI/expBG.png";
import useGameConfig from "@/hooks/useGameConfig";
import { cn } from "@/lib/utils";
import { CountdownTimer } from "../CountdownTimer";
import type { User } from "@/types/user";

type StatBarType = "health" | "energy" | "actionPoints" | "exp";

interface StatBarTypeConfig {
    fill: string;
    iconSize: string;
    image: string;
    alt: string;
}

interface SidebarStatBarProps {
    barPercentage: string;
    type: StatBarType;
    barText?: string;
    currentUser?: User;
    onClick?: () => void;
    nextTick?: number;
    isMax?: boolean;
    tooltipTitle?: string;
    tooltipDescription?: string;
}

export default function SidebarStatBar({
    barPercentage,
    type,
    barText,
    currentUser,
    onClick,
    nextTick,
    isMax,
    tooltipTitle,
    tooltipDescription,
}: SidebarStatBarProps) {
    const statBarTypes: Record<Exclude<StatBarType, "exp">, StatBarTypeConfig> = {
        health: {
            fill: "greenSliderFill",
            iconSize: "2xl:h-6 h-5 w-auto 2xl:left-[12%] left-5",
            image: hpImg,
            alt: "Health Bar",
        },
        energy: {
            fill: "yellowSliderFill",
            iconSize: "2xl:h-6 h-5 w-auto 2xl:left-[15%] left-6",
            image: energyImg,
            alt: "Energy Bar",
        },
        actionPoints: {
            fill: "blueSliderFill",
            iconSize: "2xl:h-6 h-5 w-auto 2xl:left-[13%] left-6",
            image: apImg,
            alt: "Action Points Bar",
        },
    };

    const { MAX_LEVEL_CAP } = useGameConfig();
    const isMaxLevel = (currentUser?.level ?? 0) >= MAX_LEVEL_CAP;

    const barPercentString = parseFloat(barPercentage) * 112.1 + "%";

    const variableBorderSlice = `${parseFloat(barPercentage) * (type === "exp" ? 400 : 375)}% fill`;
    const borderSlice = parseFloat(barPercentage) < 0.13 ? variableBorderSlice : "49% fill";

    // EXP BAR //
    if (type === "exp") {
        const expBarPercent = isMaxLevel ? 1 : parseFloat(barPercentage);
        const expBarText = isMaxLevel ? "MAX" : `${barText}`;
        const expBorderSlice = isMaxLevel ? "49% fill" : borderSlice;
        const barPercentStringExp = expBarPercent * 100.5 + "%";
        return (
            <div
                data-tooltip-id="statbar-tooltip"
                data-tooltip-statname="EXP"
                data-tooltip-statamount={(currentUser?.xpForNextLevel ?? 0) - (currentUser?.xp ?? 0)}
                data-tooltip-statdesc="EXP Remaining"
                className="expSliderBG relative z-10 mt-3 w-full scale-[0.85] 2xl:scale-100"
                onClick={onClick}
            >
                <div className="-top-[2.7rem] absolute left-[20%] z-10 w-full font-lili text-white text-xl dark:text-gray-50">
                    <span className="text-stroke-md">{currentUser?.username}</span>
                    <span className="font-light font-lili text-gray-900 text-sm dark:text-gray-300">
                        {" "}
                        #{currentUser?.id}
                    </span>
                </div>
                <div
                    className="expSliderFill -top-[0.84rem] -left-[0.85rem] absolute ml-[1.65rem] flex h-7 dark:brightness-90"
                    style={{
                        width: barPercentStringExp,
                        borderImageSlice: expBorderSlice,
                    }}
                ></div>
                <div className="-translate-y-1/2 absolute top-[30%] flex w-full items-center justify-center font-lili text-sm text-white">
                    <p className="">{expBarText}</p>
                </div>
                <small className="-top-10 -left-2.5 absolute z-10 w-10 font-lili text-xs brightness-95 dark:text-slate-300">
                    LVL
                </small>
                <img className="-top-6 -left-5 absolute w-10 brightness-95" src={expImg} alt="" />
                <p className="-top-4 -left-5 absolute w-10 text-center font-bold font-body text-lg text-stroke-sm text-white dark:text-stroke-s-sm">
                    {currentUser?.level}
                </p>
            </div>
        );
    }

    return (
        <div
            data-tooltip-id="statbar-tooltip"
            data-tooltip-statname={tooltipTitle}
            data-tooltip-statdesc={tooltipDescription}
            className="sliderBG relative h-8 w-full select-none 2xl:mb-1 2xl:h-10"
            onClick={onClick}
        >
            <div
                style={{ width: barPercentString, borderImageSlice: borderSlice }}
                className={cn(
                    "-top-[0.9rem] -left-[0.85rem] absolute flex h-6 2xl:h-8 dark:brightness-90",
                    type && statBarTypes[type as keyof typeof statBarTypes].fill
                )}
            ></div>
            <img
                src={type ? statBarTypes[type as keyof typeof statBarTypes].image : ""}
                alt={type ? statBarTypes[type as keyof typeof statBarTypes].alt : ""}
                className={cn(
                    "-top-0.5 -translate-y-1/2 absolute 2xl:top-[35%]",
                    type && statBarTypes[type as keyof typeof statBarTypes].iconSize
                )}
            />
            <p
                className={cn(
                    (barText?.length ?? 0) > 10 ? "text-sm" : "text-sm 2xl:text-base",
                    "-top-0.5 -translate-y-1/2 -translate-x-1/2 absolute left-1/2 font-lili text-stroke-sm text-white 2xl:top-[30%]"
                )}
            >
                {barText}
            </p>
            {nextTick && !isMax && !isNaN(nextTick) ? (
                <p className="-top-1.5 absolute right-0 font-lili text-stroke-sm text-white text-xs">
                    <CountdownTimer targetDate={nextTick} />
                </p>
            ) : null}
        </div>
    );
}
