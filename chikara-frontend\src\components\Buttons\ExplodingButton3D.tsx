import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import React, { useState } from "react";
import "./button.css";

const btn3dVariants = {
    primary: {
        edge: "btn3dPrimaryEdge",
        front: "btn3dPrimary",
    },
    danger: {
        edge: "btn3dDangerEdge",
        front: "btn3dDanger",
    },
    secondary: {
        edge: "btn3dSecondaryEdge",
        front: "btn3dSecondary",
    },
    tertiary: {
        edge: "btn3dTertiaryEdge",
        front: "btn3dTertiary",
    },
} as const;

type ButtonVariant = keyof typeof btn3dVariants;

interface ExplodingButton3DProps {
    text?: string;
    onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
    disabled?: boolean;
    className?: string;
    children?: React.ReactNode;
    height?: string;
    rounded?: string;
    type?: ButtonVariant;
}

function Button3D({
    text,
    onClick,
    disabled = false,
    className,
    children,
    height = "h-10!",
    rounded = "rounded-[5px]",
    type = "primary",
}: ExplodingButton3DProps) {
    const [clickCount, setClickCount] = useState(0);
    const [isExploded, setIsExploded] = useState(false);

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        if (disabled) return;

        if (clickCount < 2) {
            setClickCount(clickCount + 1);
            onClick && onClick(event);
        } else {
            setIsExploded(true);
            //   setTimeout(() => setIsExploded(false), 2000); // Reset after explosion animation
        }
    };

    const variant = disabled ? btn3dVariants["primary"] : btn3dVariants[type];

    const particleCount = 75;
    const particles = Array.from({ length: particleCount });

    return (
        <div className="relative">
            <AnimatePresence>
                {!isExploded && (
                    <motion.button
                        disabled={disabled}
                        initial={{ opacity: 1 }}
                        exit={{ opacity: 0, transition: { duration: 0 } }}
                        className={cn(
                            "btn3dPush hover:filter-brightness-110 relative mt-1 w-full cursor-pointer border-0 bg-transparent p-0 text-white outline-offset-4 md:min-w-20",
                            className,
                            height,
                            disabled && "grayscale"
                        )}
                        onClick={handleClick}
                    >
                        <span className={cn("btn3dShadow absolute top-0 left-0 size-full bg-black/20", rounded)}></span>
                        <span className={cn("btn3dEdge absolute inset-0 m-0 size-full", variant?.edge, rounded)}></span>
                        <span
                            className={cn(
                                "btn3dFront relative flex items-center justify-center px-4 font-medium text-stroke-sm",
                                variant?.front,
                                rounded,
                                height,
                                disabled && "text-gray-400"
                            )}
                        >
                            {children} {text && text}
                        </span>
                    </motion.button>
                )}
            </AnimatePresence>
            {isExploded && (
                <div className="absolute inset-0 flex items-center justify-center">
                    {particles.map((_, index) => (
                        <motion.div
                            key={index}
                            className={cn("absolute rounded-full", variant?.front)}
                            initial={{ x: 0, y: 0, width: "4px", height: "4px" }}
                            animate={{
                                x: (Math.random() - 0.5) * 400,
                                y: (Math.random() - 0.5) * 400,
                                opacity: 0,
                                scale: Math.random() * 3 + 1,
                            }}
                            transition={{
                                duration: Math.random() * 1 + 1,
                                ease: "easeOut",
                            }}
                        />
                    ))}
                </div>
            )}
        </div>
    );
}

export default Button3D;
