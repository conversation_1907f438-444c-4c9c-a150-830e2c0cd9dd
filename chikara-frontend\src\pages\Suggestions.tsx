import Button from "@/components/Buttons/Button";
import IconButton from "@/components/Buttons/IconButton";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import { Modal } from "@/components/Modal/Modal";
import Spinner from "@/components/Spinners/Spinner";
import { capitaliseFirstLetter } from "@/helpers/capitaliseFirstLetter";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { cn } from "@/lib/utils";
import { useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { Link, useSearchParams } from "react-router-dom";
import { useGetSuggestions, useGetVoteHistory } from "@/features/suggestions/api/useSuggestions";
import { useSuggestionVote, useCreateSuggestion } from "@/features/suggestions/api/useSuggestionMutations";
import { SuggestionComments } from "../features/suggestions/components/SuggestionComments";
import { formatTimeToNow } from "@/helpers/dateHelpers";
import type { User } from "@/types/user";
import type { QueryClient } from "@tanstack/react-query";

// Types
type SuggestionState = "New" | "Accepted" | "Completed" | "Denied";
type VoteType = "upvote" | "downvote";

interface SuggestionUser {
    id: number;
    username: string;
}

interface Suggestion {
    id: number;
    title: string;
    content: string;
    state?: SuggestionState;
    upvotes: number;
    downvotes: number;
    totalComments: number;
    userId: number;
    user: SuggestionUser;
    createdAt: string;
    haveVoted?: VoteType | null;
}

interface VoteHistory {
    suggestionId: number;
    voteType: VoteType;
}

interface Heading {
    title: string;
    type: string;
    amount?: number;
}

interface Tab {
    name: string;
    current: boolean;
}

interface SingleSuggestionProps {
    suggestion: Suggestion;
    setCurrentSuggestion?: (suggestion: Suggestion) => void;
}

interface OpenedSuggestionProps {
    suggestion: Suggestion;
    suggestionVote: (suggestion: Suggestion, type: VoteType, onCurrent?: boolean) => Promise<any>;
    queryClient?: QueryClient;
    currentUser?: User;
}

interface MakeSuggestionModalProps {
    modalOpen: boolean;
    setModalOpen: (open: boolean) => void;
    suggestionVote: (suggestion: Suggestion, type: VoteType, onCurrent?: boolean) => Promise<any>;
}

const getTagColour = (tag?: string): string => {
    if (!tag || tag === "New") return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
    if (tag === "Accepted") return "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300";
    if (tag === "Completed") return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
    if (tag === "Denied") return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
    return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
};

const getVoteTotalText = (voteTotal: number): string => {
    let voteTotalText = "text-gray-200";
    if (voteTotal < 0) {
        voteTotalText = "text-red-500 text-[1rem]";
    }
    if (voteTotal > 99) {
        voteTotalText = "text-gray-200 text-sm";
    }
    if (voteTotal < -99) {
        voteTotalText = "text-red-500 text-xs";
    }
    return voteTotalText;
};

function Suggestions() {
    const { isLoading, data: suggestionList } = useGetSuggestions();
    const { data: voteHistory } = useGetVoteHistory();
    const [currentSuggestion, setCurrentSuggestion] = useState<Suggestion | null>(null);
    const [page, setPage] = useState<string>("new");
    const [modalOpen, setModalOpen] = useState<boolean>(false);
    const [searchParams, setSearchParams] = useSearchParams();
    const pageType = searchParams.get("type");
    const pageID = searchParams.get("id");
    const queryClient = useQueryClient();
    const { data: currentUser } = useFetchCurrentUser();
    const suggestionVoteMutation = useSuggestionVote();

    const suggestionsWithVotes = suggestionList?.map((list: any) => {
        const haveVoted = voteHistory?.find((vote: VoteHistory) => vote.suggestionId === list.id);
        return {
            ...list,
            ...(haveVoted ? { haveVoted: haveVoted.voteType } : { haveVoted: null }),
        };
    });

    useEffect(() => {
        if (pageType) {
            setPage(pageType);
        }
        if (pageID && pageID.length > 0) {
            const selectedSuggestion = suggestionsWithVotes?.find((s: Suggestion) => s.id === parseInt(pageID));

            setCurrentSuggestion(selectedSuggestion || null);
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [pageID, suggestionList, voteHistory]);

    const isCurrent = (link: string): boolean => {
        if (page === link) return true;
        return false;
    };

    const newSuggestions = suggestionsWithVotes?.filter((s: Suggestion) => !s.state || s.state === "New");
    const acceptedSuggestions = suggestionsWithVotes?.filter((s: Suggestion) => s.state === "Accepted");
    const completedSuggestions = suggestionsWithVotes?.filter((s: Suggestion) => s.state === "Completed");
    const deniedSuggestions = suggestionsWithVotes?.filter((s: Suggestion) => s.state === "Denied");

    const headings: Heading[] = [
        { title: "New", type: "new", amount: newSuggestions?.length },
        { title: "Accepted", type: "accepted", amount: acceptedSuggestions?.length },
        { title: "Completed", type: "completed", amount: completedSuggestions?.length },
        { title: "Denied", type: "denied", amount: deniedSuggestions?.length },
    ];
    let selectedSuggestions: Suggestion[] = [];
    if (!page || page === "new") selectedSuggestions = newSuggestions || [];
    if (page === "accepted") selectedSuggestions = acceptedSuggestions || [];
    if (page === "completed") selectedSuggestions = completedSuggestions || [];
    if (page === "denied") selectedSuggestions = deniedSuggestions || [];

    const handleTabClick = (type: string): void => {
        setPage(type);
        setCurrentSuggestion(null);
    };

    async function suggestionVote(suggestion: Suggestion, type: VoteType, onCurrent = true): Promise<any> {
        try {
            const response = await suggestionVoteMutation.mutateAsync({
                suggestionId: suggestion.id,
                vote: type,
            });
            if (onCurrent) {
                setCurrentSuggestion({
                    ...suggestion,
                    upvotes: response.upvotes,
                    downvotes: response.downvotes,
                    haveVoted: type,
                });
            }
            return response;
        } catch (error) {
            // Error handling is done in the mutation hook
            console.error("Vote error:", error);
        }
    }

    return (
        <div className="mt-6 mb-8 md:mx-auto md:my-0 md:max-w-6xl">
            <div className="mx-1 mb-6 rounded-lg bg-white px-6 py-4 pb-2 shadow-sm md:mx-0 dark:border dark:border-slate-700 dark:bg-slate-800">
                <div className="mb-2 flex flex-col items-center justify-between border-gray-200 border-b pb-2 md:flex-row dark:border-gray-700">
                    <div>
                        <ul className="flex flex-wrap items-center gap-3 py-2 font-medium text-gray-700 text-sm md:gap-5">
                            {headings?.map((heading) => (
                                <li key={heading.title} className="block">
                                    <Link
                                        to={`/suggestions?type=${heading.type}`}
                                        className={cn(
                                            isCurrent(heading.type)
                                                ? "bg-linear-to-b from-blue-500 to-indigo-600 text-white hover:bg-blue-800"
                                                : "text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-slate-900 dark:hover:text-gray-100 ",
                                            "inline-block rounded-sm px-3 py-2 text-center text-base transition duration-150 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-indigo-300 active:outline-hidden active:ring-2 active:ring-indigo-300 dark:active:ring-indigo-900 dark:focus:ring-indigo-900"
                                        )}
                                        onClick={() => handleTabClick(heading.type)}
                                    >
                                        {" "}
                                        {heading.title}{" "}
                                        {heading.amount && heading.amount > 0 ? (
                                            <span className="ml-1 hidden h-4 min-w-4 items-center justify-center rounded-sm border border-black bg-yellow-500 px-1 text-white text-xs md:inline-flex">
                                                {heading.amount}
                                            </span>
                                        ) : (
                                            <span className="ml-1 hidden h-4 min-w-4 items-center justify-center rounded-sm border border-black bg-gray-500 px-1 text-white text-xs md:inline-flex">
                                                0
                                            </span>
                                        )}
                                    </Link>
                                </li>
                            ))}
                        </ul>
                    </div>
                    <div>
                        <ul className="flex flex-wrap items-center py-2 font-medium text-gray-700 text-sm">
                            <div className="mr-16 flex gap-5 md:mr-0 md:gap-3">
                                <Link to="/polls">
                                    <IconButton
                                        className="my-auto size-9 fill-white/65"
                                        iconClassName="h-[1.3rem] w-[1.3rem] fill-white/65"
                                        icon="poll"
                                    />
                                </Link>
                                <Button
                                    variant="primary"
                                    className="text-stroke-0! text-sm!"
                                    onClick={() => setModalOpen(true)}
                                >
                                    ✨ <span className="mx-1 dark:text-stroke-sm">Make A Suggestion</span> ✨
                                </Button>
                            </div>
                        </ul>
                    </div>
                </div>
                <div>
                    <>
                        {isLoading ? (
                            <Spinner center />
                        ) : (
                            <>
                                {!selectedSuggestions || selectedSuggestions.length === 0 ? (
                                    <p className="my-5 text-center text-gray-100 text-xl">
                                        No suggestions here right now
                                    </p>
                                ) : (
                                    <>
                                        {!currentSuggestion ? (
                                            selectedSuggestions?.map((suggestion) => (
                                                <SingleSuggestion
                                                    key={suggestion.id}
                                                    suggestion={suggestion}
                                                    setCurrentSuggestion={setCurrentSuggestion}
                                                />
                                            ))
                                        ) : (
                                            <OpenedSuggestion
                                                suggestion={currentSuggestion}
                                                suggestionVote={suggestionVote}
                                                queryClient={queryClient}
                                                currentUser={currentUser}
                                            />
                                        )}
                                    </>
                                )}
                            </>
                        )}
                    </>
                </div>
                <MakeSuggestionModal
                    modalOpen={modalOpen}
                    setModalOpen={setModalOpen}
                    suggestionVote={suggestionVote}
                />
            </div>
        </div>
    );
}

const SingleSuggestion = ({ suggestion }: SingleSuggestionProps) => {
    const voteTotal = suggestion.upvotes - suggestion.downvotes;

    return (
        <div key={suggestion.id} className="flex items-center py-4">
            <Link to={`/suggestions?id=${suggestion.id}`}>
                <button className="flex h-[63px] max-w-[35px] flex-col items-center rounded-sm bg-gray-100 p-2 hover:bg-gray-200 dark:bg-slate-900 dark:text-gray-200">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        width="19"
                        height="19"
                        className="main-grid-item-icon"
                        fill="none"
                        stroke="currentColor"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                    >
                        <line x1="12" x2="12" y1="19" y2="5"></line>
                        <polyline points="5 12 12 5 19 12"></polyline>
                    </svg>
                    <p className={cn("text-lg", getVoteTotalText(voteTotal))}>{voteTotal}</p>
                </button>
            </Link>
            <div className="mx-4 flex-1">
                <Link to={`/suggestions?id=${suggestion.id}`}>
                    <div className="mb-1 inline-block cursor-pointer text-[0.95rem] text-slate-800 md:text-base dark:text-gray-100">
                        {suggestion.title}
                    </div>
                </Link>
                <div className="mb-1 flex items-center md:mb-0">
                    <p
                        className={cn(
                            "mr-2 rounded-full px-2.5 py-0.5 font-medium text-xs",
                            getTagColour(suggestion.state)
                        )}
                    >
                        {!suggestion.state ? "New" : capitaliseFirstLetter(suggestion.state)}
                    </p>
                    <p className="mr-4 font-normal text-gray-300 text-sm">
                        Created {formatTimeToNow(suggestion?.createdAt)} ago
                    </p>
                    <Link
                        to={`/profile/${suggestion.userId}`}
                        className="hidden font-normal text-blue-500 text-sm md:block dark:text-blue-400"
                    >
                        <DisplayAvatar src={suggestion.user} className="mr-1 inline h-5 w-auto rounded-full" />
                        {suggestion?.user?.username}
                        <small className="ml-0.5 text-slate-400">#{suggestion.userId}</small>
                    </Link>
                </div>
                <Link
                    to={`/profile/${suggestion?.userId}`}
                    className="font-normal text-blue-500 text-sm md:hidden dark:text-blue-400"
                >
                    {suggestion?.user?.username}
                    <small className="ml-0.5 text-slate-400">#{suggestion.userId}</small>
                </Link>
            </div>

            <div className="hidden md:block">
                <Link to={`/suggestions?id=${suggestion.id}`}>
                    <div className="block cursor-pointer font-normal text-slate-800 text-sm dark:text-slate-100">
                        {suggestion?.totalComments} comment{suggestion?.totalComments !== 1 ? "s" : ""}
                    </div>
                    <div className="block cursor-pointer font-normal text-slate-500 text-xs dark:text-slate-400">
                        View Details →
                    </div>
                </Link>
            </div>
        </div>
    );
};

const OpenedSuggestion = ({ suggestion, suggestionVote, currentUser }: OpenedSuggestionProps) => {
    const voteTotal = suggestion.upvotes - suggestion.downvotes;

    return (
        <>
            <div className="relative flex items-center py-4">
                <Link
                    to={`/profile/${suggestion?.userId}`}
                    className="-top-0.5 absolute right-2 flex md:top-2 md:right-5"
                >
                    <DisplayAvatar src={suggestion.user} className="mr-2 inline h-5 w-auto rounded-full" />
                    <p className="text-blue-500">
                        {suggestion?.user?.username} <small className="text-gray-400">#{suggestion.userId}</small>
                    </p>{" "}
                </Link>

                <div className="flex flex-col gap-2">
                    <button
                        className={cn(
                            suggestion.haveVoted === "upvote"
                                ? "bg-blue-500"
                                : "bg-gray-100 dark:bg-slate-900 dark:hover:bg-slate-600",
                            "flex flex-col items-center rounded-sm p-2 dark:text-gray-200"
                        )}
                        onClick={() => suggestionVote(suggestion, "upvote")}
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            width="19"
                            height="19"
                            className="main-grid-item-icon"
                            fill="none"
                            stroke="currentColor"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                        >
                            <line x1="12" x2="12" y1="19" y2="5"></line>
                            <polyline points="5 12 12 5 19 12"></polyline>
                        </svg>
                    </button>
                    <div className="flex flex-col items-center rounded-sm bg-gray-100 p-1 dark:bg-blue-600 dark:text-gray-200">
                        <p className={cn("text-lg", getVoteTotalText(voteTotal))}>{voteTotal}</p>
                    </div>

                    <button
                        className={cn(
                            suggestion.haveVoted === "downvote"
                                ? "bg-blue-500"
                                : "bg-gray-100 dark:bg-slate-900 dark:hover:bg-slate-600",
                            "flex flex-col items-center rounded-sm p-2 dark:text-gray-200"
                        )}
                        onClick={() => suggestionVote(suggestion, "downvote")}
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            width="19"
                            height="19"
                            className="main-grid-item-icon rotate-180"
                            fill="none"
                            stroke="currentColor"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                        >
                            <line x1="12" x2="12" y1="19" y2="5"></line>
                            <polyline points="5 12 12 5 19 12"></polyline>
                        </svg>
                    </button>
                </div>
                <div className="mx-4 flex-1">
                    <h2 className="mb-2 font-medium text-slate-800 text-xl md:text-2xl dark:text-slate-100">
                        {suggestion.title}
                    </h2>
                    <p className="mb-2 w-full whitespace-pre-wrap font-medium text-base text-slate-700 dark:text-slate-200">
                        {suggestion.content}
                    </p>
                    <div className="flex items-center">
                        <span
                            className={cn(
                                getTagColour(suggestion.state),
                                "mr-2 rounded-full px-2.5 py-0.5 font-medium text-xs"
                            )}
                        >
                            {!suggestion.state ? "New" : capitaliseFirstLetter(suggestion.state)}
                        </span>

                        <p className="font-medium text-gray-300 text-xs">
                            Created {formatTimeToNow(suggestion?.createdAt)} ago
                        </p>
                    </div>
                </div>
            </div>
            <SuggestionComments formatTime={formatTimeToNow} suggestion={suggestion} currentUser={currentUser} />
        </>
    );
};

const MakeSuggestionModal = ({ modalOpen, setModalOpen, suggestionVote }: MakeSuggestionModalProps) => {
    const [title, setTitle] = useState<string>("");
    const [content, setContent] = useState<string>("");
    const createSuggestionMutation = useCreateSuggestion();

    async function submitSuggestion(): Promise<any> {
        if (title.length < 5) {
            toast.error("Please enter a title");
            return;
        }
        if (content.length < 10) {
            toast.error("Please enter suggestion details");
            return;
        }

        try {
            const response = await createSuggestionMutation.mutateAsync({
                title: title,
                content: content,
            });

            suggestionVote(response, "upvote", false);
            setTitle("");
            setContent("");
            setModalOpen(false);
            return response;
        } catch (error) {
            // Error handling is done in the mutation hook
            console.error("Create suggestion error:", error);
        }
    }

    return (
        <Modal
            showClose
            open={modalOpen}
            title="Make A Suggestion"
            iconBackground="shadow-lg"
            modalMaxWidth="max-w-3xl!"
            Icon={() => (
                <img
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/cgBzeee.png`}
                    alt=""
                    className="mt-0.5 h-11 w-auto"
                />
            )}
            onOpenChange={setModalOpen}
        >
            <div className="flex flex-col md:mx-12">
                <div>
                    <label
                        className="mb-2 block text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200"
                        htmlFor="inputtitle"
                    >
                        Title<span className="text-red-500"> *</span>
                    </label>
                    <div className="mt-1 flex rounded-md shadow-xs">
                        <input
                            type="text"
                            name="inputtitle"
                            id="inputtitle"
                            className="block w-full min-w-0 flex-1 rounded-md border-gray-300 px-3 py-2 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200"
                            value={title}
                            onChange={(e) => {
                                setTitle(e.target.value);
                            }}
                        />
                    </div>
                </div>

                <div>
                    <label
                        htmlFor="about"
                        className="mt-4 mb-2 block font-bold text-gray-700 text-sm uppercase tracking-wide dark:font-normal dark:text-gray-300"
                    >
                        Details<span className="text-red-500"> *</span>
                    </label>
                    <div className="mt-1 mb-4">
                        <textarea
                            id="about"
                            name="about"
                            rows={3}
                            className="mt-1 block h-36 w-full rounded-md border-gray-300 shadow-xs focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm md:h-52 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200"
                            value={content}
                            onChange={(e) => {
                                setContent(e.target.value);
                            }}
                        />
                    </div>
                </div>
                <button
                    type="button"
                    className="darkBlueButtonBGSVG mx-auto flex h-18 w-3/4 items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs md:mt-3 dark:text-slate-200"
                    onClick={() => submitSuggestion()}
                >
                    Submit
                </button>
            </div>
        </Modal>
    );
};

export default Suggestions;
