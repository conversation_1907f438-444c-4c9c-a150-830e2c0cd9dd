import { Outlet, useLocation } from "react-router-dom";
import classroom1Day from "@/assets/images/classroom1Day.webp";
import TechnicalError from "@/components/TechnicalError";

export default function AuthLayout() {
    const location = useLocation();
    let bgimage = classroom1Day;
    if (location.pathname === "/register") bgimage = classroom1Day;

    if (location.pathname === "/error") return <TechnicalError />;

    const getHeader = (): string | undefined => {
        if (location.pathname === "/login") return "Student Login";
        if (location.pathname === "/register") return "Student Registration";
        if (location.pathname === "/forgotpassword") return "Forgot Password";
        if (location.pathname === "/passwordReset") return "Reset Password";
    };

    if (location.pathname === "/callback") return <Outlet />;

    return (
        <>
            <img src={bgimage} className="absolute inset-0 z-0 h-screen w-screen object-cover " alt="" />
            <div className="vignette-lg absolute inset-0 z-5 h-screen w-screen object-cover backdrop-blur-xs"></div>

            <div className="-translate-x-1/2 -translate-y-1/2 fixed top-[49%] left-1/2 z-40 flex w-[96%] flex-col md:top-[42%]">
                <img
                    src="https://ik.imagekit.io/e0qbzc0rw/logo_WsWnrmBaj.png?updatedAt=1696448653491"
                    alt=""
                    className="mx-auto 3xl:h-80 h-48 w-auto md:h-76"
                />
                <div className="grid max-w-lg rounded-lg border border-[#282839] bg-slate-900 text-gray-200 text-stroke-s-sm shadow-lg duration-200 focus-visible:outline-hidden sm:rounded-lg md:mx-auto md:w-full">
                    <div className="modalHeaderBackground relative flex flex-col space-y-1.5 rounded-t-lg bg-gray-950/90 px-6 py-1 text-center bg-blend-overlay sm:text-left">
                        <div className="my-3 flex h-full ">
                            <div className="text-custom-yellow! m-auto mt-2.5 text-center text-2xl text-stroke-s-md text-stroke-sm uppercase leading-none">
                                {getHeader()}
                            </div>
                        </div>
                    </div>
                    <hr className="h-0.5 border-[#282839] shadow-xl" />
                    <div className="gradientBackground absolute bottom-0 z-[-1] size-full opacity-[0.02]"></div>

                    {/* // CONTENT */}
                    <div className="vignette-sm">
                        <Outlet />
                    </div>
                </div>
            </div>
        </>
    );
}
