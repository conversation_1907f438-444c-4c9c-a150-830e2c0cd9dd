import jailBars from "@/assets/images/jailbars.png";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import useGameConfig from "@/hooks/useGameConfig";
import { Link } from "react-router-dom";
import StatusEffects from "../../StatusEffects";
import { CountdownTimer } from "../CountdownTimer";
import SidebarStatBar from "./SidebarStatBar";
import type { User } from "@/types/user";
import { useGetStatusEffects } from "@/hooks/api/useGetStatusEffects";

interface SidebarProfileProps {
    currentUser?: User | undefined;
}

export default function SidebarProfile({ currentUser }: SidebarProfileProps) {
    const { data: statusEffects } = useGetStatusEffects();
    const maxEnergy = 100;
    const percentEXP = Math.min(Math.max((currentUser?.xp ?? 0) / (currentUser?.xpForNextLevel ?? 1), 0), 1);

    const { ENERGY_TICK_MS } = useGameConfig();

    const percentEnergy = (currentUser?.energy ?? 0) / maxEnergy;
    const percentAP = (currentUser?.actionPoints ?? 0) / (currentUser?.maxActionPoints ?? 1);
    const percentHealth = (currentUser?.currentHealth ?? 0) / (currentUser?.health ?? 1);

    const displayCountdown = () => {
        if (currentUser?.jailedUntil !== null && currentUser?.jailedUntil !== undefined) {
            return (
                <p className="mx-auto mb-2 rounded-lg bg-red-500 px-2 py-1 text-base text-white md:text-slate-800 dark:text-gray-200">
                    Jail Timer: <CountdownTimer targetDate={Number(currentUser.jailedUntil)} />
                </p>
            );
        } else if (currentUser?.hospitalisedUntil !== null && currentUser?.hospitalisedUntil !== undefined) {
            return (
                <p className="mx-auto mb-2 rounded-lg bg-red-500 px-2 py-1 text-base text-white md:text-slate-800 dark:text-white">
                    Hospital Timer: <CountdownTimer targetDate={Number(currentUser.hospitalisedUntil)} />
                </p>
            );
        } else if (currentUser?.missionEnds !== null && currentUser?.missionEnds !== undefined) {
            return (
                <Link
                    to="/missions"
                    className="mx-auto mb-2 rounded-lg bg-blue-500 px-2 py-1 text-base text-white md:text-slate-800 dark:text-white"
                >
                    Mission Timer:{" "}
                    <CountdownTimer
                        showHours
                        showHoursText
                        targetDate={Number(currentUser.missionEnds)}
                        showSeconds={false}
                    />
                </Link>
            );
        }
        return null;
    };

    return (
        <section className="relative mx-2 select-none rounded-md border pt-2 pb-1 shadow-2xl 2xl:overflow-hidden 2xl:px-2 2xl:pb-4 dark:border-gray-800 dark:bg-gray-900">
            <div className="flex flex-col rounded-lg drop-shadow-lg">
                {displayCountdown()}
                <Link className="flex" to={`/profile/${currentUser?.id}`}>
                    <div className={`mx-auto w-[45%] shadow-sm drop-shadow-lg`}>
                        <DisplayAvatar
                            src={currentUser}
                            className={`-z-10 mx-auto h-20 rounded-lg border-2 border-black object-cover 2xl:h-24 dark:border-gray-700 ${
                                currentUser?.hospitalisedUntil !== null ? `grayscale` : ``
                            }`}
                        />
                        {currentUser?.hospitalisedUntil !== null && (
                            <div className="-mr-2 -mt-2 -translate-x-2/4 absolute top-1/2 left-1/2 rounded-lg bg-black px-1.5 py-0.5 font-lili text-base text-red-600 opacity-95">
                                <p>HOSPITALISED</p>
                            </div>
                        )}
                        {currentUser?.jailedUntil !== null && (
                            <>
                                <img
                                    alt="jailed"
                                    src={jailBars}
                                    className="-mr-2 -mt-2 absolute top-1 left-[0.1rem] rounded-lg font-medium text-red-600 text-sm"
                                />
                                <div className="-mr-2 -mt-2 -translate-x-2/4 absolute top-1/2 left-1/2 rounded-lg bg-black px-1.5 py-0.5 font-lili text-base text-red-600 opacity-95">
                                    <p>JAILED</p>
                                </div>
                            </>
                        )}
                    </div>
                </Link>

                <SidebarStatBar
                    type="exp"
                    barPercentage={percentEXP.toString()}
                    barText={`${currentUser?.xp} / ${currentUser?.xpForNextLevel}`}
                    currentUser={currentUser}
                />
                <div className="mx-1 pt-1.5 pb-2 2xl:pt-3">
                    <SidebarStatBar
                        type="health"
                        barPercentage={percentHealth.toString()}
                        barText={currentUser && `${currentUser?.currentHealth} / ${currentUser?.health}`}
                        nextTick={Number(currentUser?.nextHPTick ?? "0")}
                        isMax={(currentUser?.currentHealth ?? 0) >= (currentUser?.health ?? 0)}
                        tooltipTitle="Current Health(HP)"
                        tooltipDescription="You lose a battle when your HP reaches 0"
                    />
                    <SidebarStatBar
                        type="energy"
                        barPercentage={percentEnergy.toString()}
                        barText={currentUser && `${currentUser?.energy} / 100`}
                        nextTick={Number(currentUser?.lastEnergyTick ?? "0") + ENERGY_TICK_MS}
                        isMax={(currentUser?.energy ?? 0) >= 100}
                        tooltipTitle="Energy"
                        tooltipDescription="Used to train your stats and get stronger"
                    />
                    <SidebarStatBar
                        type="actionPoints"
                        barPercentage={percentAP.toString()}
                        nextTick={Number(currentUser?.nextAPTick ?? "0")}
                        isMax={(currentUser?.actionPoints ?? 0) >= (currentUser?.maxActionPoints ?? 0)}
                        tooltipTitle="Action Points(AP)"
                        tooltipDescription="Used for adventure mode actions and attacking other players"
                        barText={currentUser && `${currentUser?.actionPoints} / ${currentUser?.maxActionPoints}`}
                    />
                </div>
                {statusEffects && statusEffects.length > 0 && (
                    <div className="relative flex h-fit w-full scale-95 flex-col rounded-lg border-2 border-gray-200 bg-white py-1 pb-2 2xl:scale-100 2xl:pt-1 dark:border-red-500 dark:bg-gray-800">
                        <p className="-translate-x-1/2 -top-3 absolute left-1/2 rounded-md border border-white bg-slate-800 px-2 text-red-500 text-stroke-sm text-xs uppercase tracking-wide">
                            Injuries
                        </p>

                        <StatusEffects currentEffects={statusEffects} />
                    </div>
                )}
            </div>
        </section>
    );
}
