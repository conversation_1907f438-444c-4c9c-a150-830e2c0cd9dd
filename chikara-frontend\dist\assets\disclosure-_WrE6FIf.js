import{m as E,aS as x,r as l,aU as T,cP as j,aR as I,aW as h,cQ as z,bf as J,b9 as k,bl as L,bm as Q,bg as C,br as W,cR as X,bw as Y,bx as Z,by as ee,bz as te,bq as A,bA as U,bG as le,bB as g,b0 as se,b3 as V}from"./index-UBkgY7aq.js";var q;let ae=(q=E.startTransition)!=null?q:function(e){e()};var ne=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(ne||{}),re=(e=>(e[e.ToggleDisclosure=0]="ToggleDisclosure",e[e.CloseDisclosure=1]="CloseDisclosure",e[e.SetButtonId=2]="SetButtonId",e[e.SetPanelId=3]="SetPanelId",e[e.SetButtonElement=4]="SetButtonElement",e[e.SetPanelElement=5]="SetPanelElement",e))(re||{});let oe={0:e=>({...e,disclosureState:k(e.disclosureState,{0:1,1:0})}),1:e=>e.disclosureState===1?e:{...e,disclosureState:1},2(e,t){return e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId}},3(e,t){return e.panelId===t.panelId?e:{...e,panelId:t.panelId}},4(e,t){return e.buttonElement===t.element?e:{...e,buttonElement:t.element}},5(e,t){return e.panelElement===t.element?e:{...e,panelElement:t.element}}},w=l.createContext(null);w.displayName="DisclosureContext";function B(e){let t=l.useContext(w);if(t===null){let c=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(c,B),c}return t}let R=l.createContext(null);R.displayName="DisclosureAPIContext";function G(e){let t=l.useContext(R);if(t===null){let c=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(c,G),c}return t}let F=l.createContext(null);F.displayName="DisclosurePanelContext";function ue(){return l.useContext(F)}function ce(e,t){return k(t.type,oe,e,t)}let ie=l.Fragment;function de(e,t){let{defaultOpen:c=!1,...i}=e,u=l.useRef(null),d=T(t,j(r=>{u.current=r},e.as===void 0||e.as===l.Fragment)),p=l.useReducer(ce,{disclosureState:c?0:1,buttonElement:null,panelElement:null,buttonId:null,panelId:null}),[{disclosureState:s,buttonId:a},S]=p,n=I(r=>{S({type:1});let f=se(u);if(!f||!a)return;let b=r?V(r)?r:"current"in r&&V(r.current)?r.current:f.getElementById(a):f.getElementById(a);b?.focus()}),P=l.useMemo(()=>({close:n}),[n]),m=l.useMemo(()=>({open:s===0,close:n}),[s,n]),y={ref:d},v=h();return E.createElement(w.Provider,{value:p},E.createElement(R.Provider,{value:P},E.createElement(z,{value:n},E.createElement(J,{value:k(s,{0:C.Open,1:C.Closed})},v({ourProps:y,theirProps:i,slot:m,defaultTag:ie,name:"Disclosure"})))))}let pe="button";function fe(e,t){let c=l.useId(),{id:i=`headlessui-disclosure-button-${c}`,disabled:u=!1,autoFocus:d=!1,...p}=e,[s,a]=B("Disclosure.Button"),S=ue(),n=S===null?!1:S===s.panelId,P=l.useRef(null),m=T(P,t,I(o=>{if(!n)return a({type:4,element:o})}));l.useEffect(()=>{if(!n)return a({type:2,buttonId:i}),()=>{a({type:2,buttonId:null})}},[i,a,n]);let y=I(o=>{var D;if(n){if(s.disclosureState===1)return;switch(o.key){case g.Space:case g.Enter:o.preventDefault(),o.stopPropagation(),a({type:0}),(D=s.buttonElement)==null||D.focus();break}}else switch(o.key){case g.Space:case g.Enter:o.preventDefault(),o.stopPropagation(),a({type:0});break}}),v=I(o=>{switch(o.key){case g.Space:o.preventDefault();break}}),r=I(o=>{var D;le(o.currentTarget)||u||(n?(a({type:0}),(D=s.buttonElement)==null||D.focus()):a({type:0}))}),{isFocusVisible:f,focusProps:b}=Y({autoFocus:d}),{isHovered:$,hoverProps:O}=Z({isDisabled:u}),{pressed:M,pressProps:K}=ee({disabled:u}),H=l.useMemo(()=>({open:s.disclosureState===0,hover:$,active:M,disabled:u,focus:f,autofocus:d}),[s,$,M,f,u,d]),N=te(e,s.buttonElement),_=n?A({ref:m,type:N,disabled:u||void 0,autoFocus:d,onKeyDown:y,onClick:r},b,O,K):A({ref:m,id:i,type:N,"aria-expanded":s.disclosureState===0,"aria-controls":s.panelElement?s.panelId:void 0,disabled:u||void 0,autoFocus:d,onKeyDown:y,onKeyUp:v,onClick:r},b,O,K);return h()({ourProps:_,theirProps:p,slot:H,defaultTag:pe,name:"Disclosure.Button"})}let me="div",be=U.RenderStrategy|U.Static;function Ee(e,t){let c=l.useId(),{id:i=`headlessui-disclosure-panel-${c}`,transition:u=!1,...d}=e,[p,s]=B("Disclosure.Panel"),{close:a}=G("Disclosure.Panel"),[S,n]=l.useState(null),P=T(t,I($=>{ae(()=>s({type:5,element:$}))}),n);l.useEffect(()=>(s({type:3,panelId:i}),()=>{s({type:3,panelId:null})}),[i,s]);let m=L(),[y,v]=Q(u,S,m!==null?(m&C.Open)===C.Open:p.disclosureState===0),r=l.useMemo(()=>({open:p.disclosureState===0,close:a}),[p.disclosureState,a]),f={ref:P,id:i,...W(v)},b=h();return E.createElement(X,null,E.createElement(F.Provider,{value:p.panelId},b({ourProps:f,theirProps:d,slot:r,defaultTag:me,features:be,visible:y,name:"Disclosure.Panel"})))}let Se=x(de),ye=x(fe),Ie=x(Ee),ve=Object.assign(Se,{Button:ye,Panel:Ie});export{ve as V};
