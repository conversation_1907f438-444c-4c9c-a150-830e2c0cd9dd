import{r as m,b as h,c as p,t as f,j as e,o as j,G as d,L as g,h as N,c$ as b,d1 as w}from"./index-UBkgY7aq.js";import{g as y,s as v,b as k}from"./Icon_ImageIcon_Medal_Silver-OH_4ykaO.js";import{V as G}from"./ViewGangModal-Cm6TqzUI.js";function R(){const[s,r]=m.useState(!1),[t,c]=m.useState(null),{data:l,isLoading:i}=h(p.gang.getGangList.queryOptions()),{data:n}=f(),x=a=>{c(a),r(!0)},u=l?.slice(1).sort((a,o)=>o.weeklyRespect-a.weeklyRespect);return e.jsx(e.Fragment,{children:e.jsx("section",{className:"mx-auto rounded-lg bg-gray-100 py-3 md:max-w-3xl dark:bg-gray-800",children:e.jsxs("div",{className:"flex flex-col gap-2 p-1.5",children:[e.jsxs("div",{className:"relative mb-6 flex flex-col text-center text-2xl text-custom-yellow",children:[e.jsxs(j,{className:"flex justify-start md:static",to:-1,children:[e.jsx(d,{className:"h-10! w-20!",children:"Back"}),e.jsx(d,{className:"!absolute left-0! h-10! w-20! hidden! md:block! font-display text-base",children:"Back"})]}),e.jsx("p",{className:"-mb-4 mt-2 md:my-0 font-display",children:"Weekly Gang Leaderboard"})]}),e.jsxs(g,{isLoading:i,children:[e.jsx(G,{hideInviteButton:!0,open:s,setOpen:r,selectedGang:t,setSelectedGang:c,currentUser:n}),u?.map((a,o)=>e.jsx(L,{gang:a,className:"h-16",keyProp:a.id,boardIndex:o,onClick:()=>x(a)}))]})]})})})}function L({gang:s,className:r,onClick:t,keyProp:c,boardIndex:l}){return s?e.jsxs("div",{className:N("relative flex gap-1 overflow-hidden rounded-lg border-2 border-black bg-linear-to-r from-blue-700 to-blue-900 p-1",t&&"cursor-pointer hover:border-blue-500",r),onClick:t||null,children:[e.jsxs("div",{className:"relative h-full w-10",children:[l===0&&e.jsx("img",{className:"mx-auto mt-1.5 h-9 w-auto",src:y,alt:"Gold medal"}),l===1&&e.jsx("img",{className:"mx-auto mt-1.5 h-9 w-auto",src:v,alt:"Silver medal"}),l===2&&e.jsx("img",{className:"mx-auto mt-1.5 h-9 w-auto",src:k,alt:"Bronze medal"})]}),e.jsx("div",{className:"relative h-full w-20",children:e.jsx(b,{src:s,className:"mx-auto h-full w-auto"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"my-auto text-left font-body font-semibold text-custom-yellow text-stroke-sm md:text-lg",children:s.name}),e.jsxs("div",{className:"hidden h-6 items-center gap-0.5 md:flex",children:[e.jsx("img",{className:"my-auto mr-1 aspect-square h-6",src:"https://cloudflare-image.jamessut.workers.dev/ui-images/nf7boKi.png",alt:""}),e.jsxs("p",{className:"mt-0.5 text-sm text-white",children:["Lv ",s.hideout_level]})]})]}),e.jsxs("div",{className:"mr-4 flex flex-1 items-center justify-end md:mr-8",children:[e.jsx("img",{className:"h-full w-auto p-3 md:p-2",src:w,alt:""}),e.jsx("p",{className:"w-10 text-lg text-red-500",children:s.weeklyRespect})]})]},c):null}export{R as default};
