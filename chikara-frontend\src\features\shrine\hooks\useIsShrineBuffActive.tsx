import { useGetActiveShrineBuffs } from "../api/useGetActiveShrineBuffs";

function useIsShrineBuffActive(buffName: string): number {
    const { data: shrineGoal, isLoading } = useGetActiveShrineBuffs();

    if (isLoading) return 0;

    // Check if the shrine goal exists and has buffRewards
    if (!shrineGoal?.buffRewards) return 0;

    // Check if the specific buff is active in the buffRewards
    const buffRewards = shrineGoal.buffRewards as Record<string, { value: number }>;
    const activeBuff = buffRewards[buffName];

    return activeBuff?.value ?? 0;
}

export default useIsShrineBuffActive;
