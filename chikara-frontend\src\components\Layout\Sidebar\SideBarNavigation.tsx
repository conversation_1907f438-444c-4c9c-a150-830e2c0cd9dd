import { hospitalisedNavItems, jailedNavItems, navItems } from "@/helpers/navItems";
import { cn } from "@/lib/utils";
import { Fragment } from "react";
import { useNormalStore } from "../../../app/store/stores";
import SideBarGridItem from "./SideBarGridItem";
import SideBarGridItemThin from "./SideBarGridItemThin";
import type { User } from "@/types/user";
import type { QuestWithProgress } from "@/types/quest";

interface SideBarNavigationProps {
    availableQuests?: QuestWithProgress[] | undefined;
    currentUser?: User | undefined;
}

export default function SideBarNavigation({ availableQuests, currentUser }: SideBarNavigationProps) {
    const hospitalised = (currentUser?.hospitalisedUntil ?? 0) > 0;
    const jailed = (currentUser?.jailedUntil ?? 0) > 0;
    const inFight = currentUser?.battleValidUntil;
    const { preventNavigation } = useNormalStore();
    let navigation: ReturnType<typeof navItems> = [];

    if (!hospitalised && !jailed) {
        navigation = navItems(currentUser?.userType);
    } else if (hospitalised) {
        navigation = hospitalisedNavItems(currentUser?.userType);
    } else {
        navigation = jailedNavItems(currentUser?.userType);
    }

    if (preventNavigation) {
        return (
            <div
                className={cn(
                    jailed || hospitalised ? "h-2/6" : "h-1/2",
                    "flex flex-1 flex-col px-2 pt-2 2xl:flex-initial"
                )}
            >
                <div
                    className={
                        "hidden size-full grid-cols-3 gap-x-2 gap-y-4 px-1 2xl:mt-1 2xl:grid 2xl:grid-cols-2 2xl:p-1"
                    }
                ></div>

                <div className={"flex size-full flex-1 flex-col gap-y-2 px-1 2xl:hidden"}></div>
            </div>
        );
    }

    return (
        <div
            className={cn(
                jailed || hospitalised ? "h-2/6" : "h-[45%]",
                "flex flex-1 flex-col px-2 pt-2 2xl:flex-initial"
            )}
        >
            <div
                className={
                    "hidden size-full grid-cols-3 gap-x-2 gap-y-4 px-1 2xl:mt-1 2xl:grid 2xl:grid-cols-2 2xl:p-1"
                }
            >
                {navigation.map((item, index) => (
                    <Fragment key={index}>
                        {item.thin ? null : (
                            <SideBarGridItem item={item} inFight={inFight} availableQuests={availableQuests?.length} />
                        )}
                    </Fragment>
                ))}
                <div className={cn("col-span-2 grid max-h-24 w-full grid-cols-1 gap-x-2 gap-y-[0.6rem]")}>
                    {navigation.map((item, index) => (
                        <Fragment key={index}>
                            {item.thin ? <SideBarGridItemThin item={item} inFight={inFight} /> : null}
                        </Fragment>
                    ))}
                </div>
            </div>

            <div className={"flex size-full flex-1 flex-col gap-y-2 px-1 2xl:hidden"}>
                {navigation.map((item, index) => (
                    <Fragment key={index}>
                        <SideBarGridItemThin item={item} inFight={inFight} />
                    </Fragment>
                ))}
            </div>
        </div>
    );
}
