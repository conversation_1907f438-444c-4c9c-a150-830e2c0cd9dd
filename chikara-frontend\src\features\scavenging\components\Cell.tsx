import React, { useState } from "react";
import { Cell as CellType, MultiCellResource, ScavengingItem } from "../types";
import { getResourceStyles } from "../constants";
import { displayMissingIcon } from "@/helpers/displayMissingIcon";

interface CellProps {
    cell: CellType;
    onClick: () => void;
    disabled: boolean;
    multiCellResource?: MultiCellResource; // The resource this cell belongs to
    isRevealing?: boolean; // Whether this cell is currently being revealed
    devMode?: boolean; // Whether dev mode is enabled
    devCell?: CellType; // The dev mode cell data (shows actual resource)
    itemMap?: Record<number, ScavengingItem>; // Map of itemId to item details
}

const Cell: React.FC<CellProps> = ({
    cell,
    onClick,
    disabled,
    multiCellResource,
    isRevealing = false,
    devMode = false,
    devCell,
    itemMap = {},
}) => {
    const [isAnimating, setIsAnimating] = useState(false);

    const handleClick = () => {
        if (!cell.revealed && !disabled && !isRevealing) {
            setIsAnimating(true);
            onClick();
            setTimeout(() => setIsAnimating(false), 300);
        }
    };

    // Determine if this cell is part of a completed multi-cell resource
    const isPartOfCompletedResource = multiCellResource?.fullyRevealed || false;
    const isPartOfMultiCell = cell.isPartOfMultiCell;
    const revealedCellsInResource = multiCellResource?.revealedCells || 0;
    const totalCellsInResource = multiCellResource?.cells.length || 1;

    // Health calculations
    const healthPercentage = (cell.health / cell.maxHealth) * 100;
    const isDamaged = cell.health < cell.maxHealth;
    const isNearDeath = cell.health === 1 && cell.maxHealth > 1;

    // Dev mode: show resource hints
    const showDevHint = devMode && !cell.revealed && devCell;
    const devResourceType = devCell?.resourceType || "empty";

    // Get styles for the cell's resource type
    const item = typeof cell.resourceType === "number" ? itemMap[cell.resourceType] : undefined;
    const cellStyles = getResourceStyles(cell.resourceType, item);

    // Get styles for dev hint
    const devItem = typeof devResourceType === "number" ? itemMap[devResourceType] : undefined;
    const devStyles = getResourceStyles(devResourceType, devItem);

    return (
        <div
            className={`
        w-full aspect-square h-14 rounded-md cursor-pointer flex flex-col items-center justify-center
        transition-all duration-300 relative
        ${isAnimating ? "scale-90" : "scale-100"}
        ${isRevealing ? "scale-95 opacity-75" : ""}
        ${
            cell.revealed
                ? `${cellStyles.color} shadow-lg ${cellStyles.glow}
               ${isPartOfCompletedResource ? "ring-2 ring-white/60" : ""}
               ${isPartOfMultiCell && !isPartOfCompletedResource ? "ring-1 ring-gray-400/40" : ""}`
                : showDevHint && devResourceType !== "empty"
                  ? `bg-gray-700 hover:bg-gray-600 active:bg-gray-500/50 ring-2 ${"borderColor" in devStyles ? devStyles.borderColor : "border-gray-400"}
                     ${isDamaged ? "ring-red-400/60" : ""}`
                  : `bg-gray-700 hover:bg-gray-600 active:bg-gray-500
                     ${isDamaged ? "ring-1 ring-red-400/60" : ""}
                     ${isNearDeath ? "ring-2 ring-red-500/80" : ""}`
        }
        ${disabled && !cell.revealed ? "opacity-80 cursor-not-allowed" : ""}
      `}
            onClick={handleClick}
        >
            {cell.revealed ? (
                <div className="flex flex-col items-center justify-center">
                    {item?.image ? (
                        <img src={displayMissingIcon(item.image)} alt={item.name} className="size-6 object-contain" />
                    ) : (
                        <span className="text-xl">{cellStyles.emoji}</span>
                    )}
                    {isPartOfCompletedResource && (
                        <div className="absolute -top-1 -right-1 size-3 bg-green-400 rounded-full border border-white"></div>
                    )}
                </div>
            ) : (
                <div className="flex flex-col items-center justify-center">
                    {isRevealing ? (
                        <div className="animate-spin rounded-full size-4 border-b-2 border-gray-400"></div>
                    ) : showDevHint ? (
                        <div className="flex flex-col items-center justify-center">
                            {devItem?.image ? (
                                <img
                                    src={devItem.image}
                                    alt={devItem.name}
                                    className="size-4 object-contain opacity-60"
                                />
                            ) : (
                                <span className="text-sm opacity-60">{devStyles.emoji}</span>
                            )}
                        </div>
                    ) : (
                        <div className="flex flex-col items-center justify-center">
                            <span className="text-gray-500 text-sm">?</span>
                            {/* Health indicator */}
                            {cell.maxHealth > 1 && (
                                <div className="absolute -bottom-0.5 left-1/2 -translate-x-1/2 flex space-x-0.5">
                                    {Array.from({ length: cell.maxHealth }, (_, i) => (
                                        <div
                                            key={i}
                                            className={`size-1 rounded-full ${
                                                i < cell.health
                                                    ? isNearDeath
                                                        ? "bg-red-400"
                                                        : isDamaged
                                                          ? "bg-yellow-400"
                                                          : "bg-green-400"
                                                    : "bg-gray-600"
                                            }`}
                                        />
                                    ))}
                                </div>
                            )}
                        </div>
                    )}
                    {isPartOfMultiCell && revealedCellsInResource > 0 && !isRevealing && (
                        <div className="absolute -bottom-1 -right-1 text-xs bg-blue-500 text-white rounded-full size-4 flex items-center justify-center">
                            {revealedCellsInResource}
                        </div>
                    )}
                </div>
            )}

            {/* Dev mode indicator */}
            {showDevHint && devResourceType !== "empty" && (
                <div className="absolute -top-1 -left-1 size-3 bg-yellow-400 rounded-full border border-gray-800"></div>
            )}

            {/* Health bar for damaged cells */}
            {!cell.revealed && isDamaged && cell.maxHealth > 1 && (
                <div className="absolute top-0 inset-x-0 h-0.5 bg-gray-600 rounded-t-md overflow-hidden">
                    <div
                        style={{ width: `${healthPercentage}%` }}
                        className={`h-full transition-all duration-300 ${isNearDeath ? "bg-red-500" : "bg-yellow-500"}`}
                    ></div>
                </div>
            )}

            {/* Progress indicator for multi-cell resources */}
            {isPartOfMultiCell && revealedCellsInResource > 0 && !isPartOfCompletedResource && (
                <div className="absolute bottom-0 inset-x-0 h-1 bg-gray-600 rounded-b-md overflow-hidden">
                    <div
                        className="h-full bg-blue-400 transition-all duration-300"
                        style={{ width: `${(revealedCellsInResource / totalCellsInResource) * 100}%` }}
                    ></div>
                </div>
            )}
        </div>
    );
};

export default Cell;
