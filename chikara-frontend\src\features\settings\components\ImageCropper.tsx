import React, { useRef, useEffect, useState } from "react";
import <PERSON>ropper from "react-cropper";
import "cropperjs/dist/cropper.css";
import Button from "@/components/Buttons/Button";
import { Modal } from "@/components/Modal/Modal";

interface SelectedFile {
    blob: Blob;
    name: string;
}

interface ImageCropperProps {
    src: File | null;
    open: boolean;
    setOpen: (open: boolean) => void;
    setPreview: (preview: string) => void;
    setSelected: (selected: SelectedFile) => void;
    setSrc: (src: File | null) => void;
}

interface ImageCropModalProps {
    children: React.ReactNode;
    open: boolean;
    setOpen: (open: boolean) => void;
}

const ImageCropper = ({ src, open, setOpen, setPreview, setSelected, setSrc }: ImageCropperProps) => {
    const [imageSrc, setImageSrc] = useState("");
    const [imageType, setImageType] = useState("image/jpeg");
    const [imageName, setImageName] = useState("");

    const cropperRef = useRef<any>(null);

    if (src) {
        const reader = new FileReader();
        reader.onload = () => {
            setImageSrc(reader.result as string);
            setImageType(src.type);
            setImageName(src.name);
        };
        reader.readAsDataURL(src);
    } else {
        return null;
    }

    const applyCrop = () => {
        if (cropperRef.current?.cropper) {
            cropperRef.current.cropper.getCroppedCanvas().toBlob((blob: Blob) => {
                setSelected({
                    blob: blob,
                    name: imageName,
                });
                setPreview(URL.createObjectURL(blob));
            }, imageType);
        }
        handleClose();
    };

    const handleClose = () => {
        setOpen(false);
        setSrc(null);
    };
    return (
        <ImageCropModal open={open} setOpen={handleClose}>
            <Cropper
                ref={cropperRef}
                src={imageSrc}
                style={{ height: "25rem", width: "100%" }}
                initialAspectRatio={1205 / 288}
                aspectRatio={1205 / 288}
                guides={false}
                viewMode={2}
                background={false}
                dragMode="move"
                modal={false}
                autoCropArea={1}
            />
            <Button
                className="ml-auto! w-1/4! font-body! text-lg!"
                variant="primary"
                onClick={() => {
                    applyCrop();
                }}
            >
                Apply
            </Button>
        </ImageCropModal>
    );
};

const ImageCropModal = ({ children, open, setOpen }: ImageCropModalProps) => {
    return (
        <Modal
            open={open}
            showClose={false}
            title="Edit Media"
            contentHeight="h-[60dvh]"
            modalMaxWidth="max-w-3xl!"
            onOpenChange={setOpen}
        >
            <div className="flex flex-col gap-2 p-1">{children}</div>
        </Modal>
    );
};

export default ImageCropper;
