import { displayMissingIcon } from "@/helpers/displayMissingIcon";
import { getItemFrame, getItemTypeFrame, getItemTypeIcon } from "@/helpers/itemHelpers";
import useIsTalentUnlocked from "@/hooks/api/useIsTalentUnlocked";
import { cn } from "@/lib/utils";
import { type Item, ItemTypes, type EquippedItem } from "@/types/item";
import type React from "react";

// Define types for component props and utility functions
interface ItemWithUpgrade {
    item: Item;
    upgradeLevel: number;
    isTradeable?: boolean | null;
}

interface Talent {
    modifier: number;
}

interface ItemDetails {
    itemDetails: Partial<Item> & { upgradeLevel?: number };
    upgradeLevel: number;
    isTradeable: boolean | null;
}

interface DisplayItemProps {
    item?: Item | ItemWithUpgrade | EquippedItem | null;
    className?: string;
    height?: string;
    noBackground?: boolean;
    quantity?: number | null;
    isClickable?: boolean;
    itemTypeFrame?: boolean;
    disableTooltip?: boolean;
    upgradeLevelOverride?: number | null;
}

// Utility functions for upgrade calculations
const calculateUpgradeValue = (val: number | null, upgradeLevel: number): number | null => {
    if (!val || !upgradeLevel || upgradeLevel <= 0) return val;

    const upgradeLevelModifier = 1 + upgradeLevel * 0.05;
    return Number.parseFloat((val * upgradeLevelModifier).toFixed(0));
};

const calculateUpgradeMultiplier = (multiplier: number | null, upgradeLevel: number): number | null => {
    if (!multiplier || !upgradeLevel || upgradeLevel <= 0) return multiplier;

    const basePercentage = multiplier - 1;
    const upgradeLevelModifier = upgradeLevel * 0.05;
    const newPercentage = basePercentage + basePercentage * upgradeLevelModifier;
    return Number.parseFloat((1 + newPercentage).toFixed(2));
};

const calculateUpgradeModifiers = (value: number | null, upgradeLevel: number): number | null => {
    if (!value || !upgradeLevel || upgradeLevel <= 0) return value;

    const multiplierThreshold = 2.0;
    return value < multiplierThreshold
        ? calculateUpgradeMultiplier(value, upgradeLevel)
        : calculateUpgradeValue(value, upgradeLevel);
};

// const getModifiedStatModifiers = (
//     statModifiers: ItemStatModifiers | null,
//     upgradeLevel: number,
// ): ItemStatModifiers | null => {
//     if (!statModifiers || !upgradeLevel || upgradeLevel <= 0) return statModifiers;

//     const modifiedStats: Record<string, number> = {};
//     for (const [stat, value] of Object.entries(statModifiers)) {
//         if (value !== undefined) {
//             modifiedStats[stat] = value > 1 ? (calculateUpgradeModifiers(value, upgradeLevel) ?? value) : value;
//         }
//     }

//     return modifiedStats as ItemStatModifiers;
// };

// Extract item details from different possible item structures
const getItemDetails = (
    item: Item | ItemWithUpgrade | EquippedItem,
    upgradeLevelOverride: number | null
): ItemDetails => {
    let itemDetails: Partial<Item> & { upgradeLevel?: number };
    let upgradeLevel = 0;
    let isTradeable: boolean | null = null;

    if (!("item" in item)) {
        itemDetails = item;
        upgradeLevel = itemDetails?.upgradeLevel && itemDetails.upgradeLevel > 0 ? itemDetails.upgradeLevel : 0;
    } else {
        itemDetails = item.item;
        upgradeLevel = item.upgradeLevel;
        isTradeable = item.isTradeable ?? null;
    }

    return {
        itemDetails,
        upgradeLevel: upgradeLevelOverride ?? upgradeLevel,
        isTradeable,
    };
};

// Main component
export function DisplayItem({
    item,
    className = "",
    height = "",
    noBackground = false,
    quantity = null,
    isClickable = false,
    itemTypeFrame = false,
    disableTooltip = false,
    upgradeLevelOverride = null,
}: DisplayItemProps) {
    const metabolismTalent = useIsTalentUnlocked("good_stomach") as Talent | null;

    if (!item) return <img src={displayMissingIcon(true)} alt={"Item"} className={cn("aspect-square", className)} />;

    // Process item data
    const { itemDetails, upgradeLevel, isTradeable } = getItemDetails(item, upgradeLevelOverride);

    const { itemType, image, name, damage, armour, health } = itemDetails || {};

    // Derived values
    const modifiedHealth = health && metabolismTalent ? Math.round(health * metabolismTalent.modifier) : health;
    const background = getItemFrame(itemDetails as Item);

    const jsonItem = {
        ...itemDetails,
        upgradeLevel: upgradeLevelOverride ?? upgradeLevel,
        isTradeable: isTradeable,
        damage: calculateUpgradeModifiers(damage || null, upgradeLevel),
        armour: calculateUpgradeModifiers(armour || null, upgradeLevel),
        health: modifiedHealth,

        // Parse itemEffects JSON safely if it's a string, otherwise use the original value
        itemEffects:
            typeof itemDetails?.itemEffects === "string"
                ? (() => {
                      try {
                          return JSON.parse(itemDetails.itemEffects);
                      } catch {
                          // Show nothing instead of crashing the tree
                          console.error("Invalid itemEffects JSON for item", itemDetails?.id);
                          return null;
                      }
                  })()
                : itemDetails?.itemEffects,
    };

    // Prepare tooltip properties
    const tooltipProps = !disableTooltip
        ? {
              "data-tooltip-id": "item-tooltip",
              "data-tooltip-item": JSON.stringify(jsonItem),
          }
        : {};

    // Generate item image class
    const itemImageClass = cn(
        noBackground ? "h-full" : "-translate-x-1/2 -translate-y-1/2 absolute top-[47%] left-1/2 z-5 h-3/4",
        "aspect-square",
        isClickable && "p-2! mx-auto",
        itemType === ItemTypes.recipe && "h-[55%]! brightness-125"
    );

    return (
        <div
            {...tooltipProps}
            className={cn("relative! aspect-square!", height, className, isClickable && "h-full! w-full!")}
        >
            {/* Background image */}
            {!noBackground && <img src={background} className="aspect-square! h-full bg-cover" alt="" />}

            {/* Recipe overlay */}
            {itemType === ItemTypes.recipe && (
                <img
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/vZhb8GS.png`}
                    className="-translate-x-1/2 -translate-y-1/2 absolute top-[45%] left-1/2 h-[86%] opacity-95 brightness-[0.6]"
                    alt="Recipe background"
                />
            )}

            {/* Item type frame */}
            {itemTypeFrame && (
                <div
                    style={getItemTypeFrame(itemDetails as Item)}
                    className="left-[-0.325rem] -top-1.5 absolute z-15 h-[22px] w-[21px] size-[1.4rem]! flex items-center justify-center"
                >
                    {/* <img
                        src={getItemTypeIcon(itemDetails.itemType)}
                        className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 z-20 h-3.5 w-auto object-contain object-center"
                        alt="Item type icon"
                    /> */}
                    <div className="text-xs mb-[0.2rem]">{getItemTypeIcon(itemDetails as Item)}</div>
                </div>
            )}

            {/* Item image */}
            <img
                src={displayMissingIcon(image)}
                loading="lazy"
                alt={name || "Item"}
                className={itemImageClass}
                onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                    e.currentTarget.src = displayMissingIcon(true);
                }}
            />

            {/* Quantity indicator */}
            {quantity && (
                <p className="absolute right-2 bottom-2 z-10 text-sm text-stroke-s-sm text-gray-200">{quantity}</p>
            )}
        </div>
    );
}
