import arrow from "@/assets/images/UI/arrow.gif";
import NotificationBadge from "@/components/NotificationBadge";
import StrokedText from "@/components/StrokedText";
import checkIfFirefox from "@/helpers/checkIfFirefox";
import { cn } from "@/lib/utils";
import { NavLink, useLocation } from "react-router-dom";
import { useNormalStore } from "../../../app/store/stores";

interface NavItem {
    name: string;
    href: string;
    icon: string;
    current?: string;
    external?: boolean;
    thin?: boolean;
    construction?: boolean;
}

interface SideBarGridItemProps {
    item: NavItem;
    inFight?: string | null;
    availableQuests?: number;
    displayTutorialArrow?: boolean;
}

export default function SideBarGridItem({
    item,
    inFight,
    availableQuests,
    displayTutorialArrow = false,
}: SideBarGridItemProps) {
    const location = useLocation();
    const { craftCollectReady } = useNormalStore();

    const checkCurrent = (name?: string): boolean => {
        if (!name) return false;
        return `/${name}` === location.pathname;
    };

    const isBig = false;
    const isFirefox = checkIfFirefox();
    const tutorialArrow = item.name === "Tasks" && !checkCurrent(item.current) && displayTutorialArrow;

    return (
        <NavLink
            to={!inFight ? item.href : "#"}
            aria-current={checkCurrent(item.current) ? "page" : undefined}
            className={cn(
                checkCurrent(item.current) ? "roundedBtnLightBlue" : "roundedBtnBlue ",
                "group flex w-full child-hover:scale-105 rounded-lg text-center font-medium font-accent text-stroke-md text-xs drop-shadow-lg hover:brightness-110",
                isBig ? "col-span-2" : null
            )}
        >
            {tutorialArrow && (
                <img
                    className="-rotate-90 -translate-x-1/2 -top-20 absolute left-1/2 z-100 size-24 scale-75"
                    src={arrow}
                    alt=""
                />
            )}
            <div className="max-w-full">
                {item.name === "Campus" && craftCollectReady && (
                    <NotificationBadge empty pulse className="absolute top-1 right-[5%] size-4" />
                )}

                {item.name === "Tasks" && availableQuests ? (
                    <NotificationBadge
                        amount={availableQuests}
                        className="top-1! right-[4%]! text-sm! absolute size-4"
                    />
                ) : null}
            </div>
            <img
                src={item.icon}
                alt=""
                className={cn(
                    `absolute h-[70%] group-hover:scale-105`,
                    (item.construction || inFight) && "brightness-50",
                    isBig
                        ? "-translate-y-1/2 -rotate-6 top-1/2 left-1/4"
                        : "-translate-y-1/2 -translate-x-1/2 top-[20%] left-1/2 z-10"
                )}
            />
            <span
                className={cn(
                    !isFirefox ? "backdrop-blur-[0.3px] backdrop-brightness-90" : "",
                    "relative size-full rounded-md text-white"
                )}
            >
                <span
                    className={cn(
                        item.construction && "text-gray-400 brightness-50",
                        isBig
                            ? "-translate-y-1/2 top-1/2 right-[30%] text-2xl"
                            : "right-1/2 bottom-2 translate-x-1/2 text-lg",
                        "absolute opacity-90 group-hover:scale-105",
                        checkCurrent(item.current) ? "text-custom-yellow" : "text-white group-hover:text-yellow-400"
                    )}
                >
                    <StrokedText
                        hideShadow
                        className="font-medium font-accent text-[1.35rem] text-custom-yellow text-stroke-s-md uppercase"
                    >
                        {item.name}
                    </StrokedText>
                </span>
            </span>
        </NavLink>
    );
}
