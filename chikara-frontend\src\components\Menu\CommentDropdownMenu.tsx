import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AlertTriangle, Ban, Clock, Eye, EyeOff, MoreVertical, Reply, Trash2, User } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { UserType } from "@/types/user";

interface Comment {
    userId: number;
    hidden?: boolean;
}

interface CommentDropdownMenuProps {
    userType: UserType;
    comment: Comment;
}

export default function CommentDropdownMenu({ userType, comment }: CommentDropdownMenuProps) {
    const navigate = useNavigate();

    return (
        <DropdownMenu>
            <DropdownMenuTrigger
                asChild
                className="rounded-lg border-blue-500 data-[state=open]:text-blue-500 data-[state=open]:ring-3"
            >
                <button className="py-1">
                    <MoreVertical className="size-4 cursor-pointer" />
                </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="dark w-56">
                {userType === "admin" || userType === "prefect" ? <ModeratorDropdownOptions comment={comment} /> : null}
                {userType === "admin" ? <AdminDropdownOptions comment={comment} /> : null}
                <DropdownMenuGroup>
                    <DropdownMenuItem>
                        <div className="flex cursor-default opacity-50">
                            <AlertTriangle className="my-auto mr-2 size-4" />
                            Report
                        </div>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                        onClick={() => {
                            navigate(`/profile/${comment?.userId}`);
                        }}
                    >
                        <User className="mr-2 size-4" />
                        Profile
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                        <Reply className="mr-2 size-4" />
                        Reply
                    </DropdownMenuItem>
                </DropdownMenuGroup>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}

interface DropdownOptionsProps {
    comment: Comment;
}

const AdminDropdownOptions = ({ comment }: DropdownOptionsProps) => {
    return (
        <>
            <DropdownMenuGroup>
                <DropdownMenuItem onSelect={() => {}}>
                    <Trash2 className="mr-2 size-4" />
                    Delete Comment
                </DropdownMenuItem>
                <DropdownMenuItem onSelect={() => {}}>
                    <Ban className="mr-2 size-4" />
                    Ban User
                </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
        </>
    );
};

const ModeratorDropdownOptions = ({ comment }: DropdownOptionsProps) => {
    return (
        <>
            <DropdownMenuLabel className="font-bold font-body">Moderation</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
                {comment?.hidden ? (
                    <DropdownMenuItem onSelect={() => {}}>
                        <Eye className="mr-2 size-4" />
                        Unhide Comment
                    </DropdownMenuItem>
                ) : (
                    <DropdownMenuItem onSelect={() => {}}>
                        <EyeOff className="mr-2 size-4" />
                        Hide Comment
                    </DropdownMenuItem>
                )}
                <DropdownMenuItem onSelect={() => {}}>
                    <Clock className="mr-2 size-4" />1 day Timeout
                </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
        </>
    );
};
