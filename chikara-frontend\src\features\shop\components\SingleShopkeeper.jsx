import yenImg from "@/assets/icons/UI/yen.png";
import Button from "@/components/Buttons/Button";
import TraderRep from "@/components/TraderRep";
import { capitaliseFirstLetter } from "@/helpers/capitaliseFirstLetter";
import { displayMissingIcon } from "@/helpers/displayMissingIcon";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { cn } from "@/lib/utils";
import { ArrowLeft } from "lucide-react";
import { Link } from "react-router-dom";

const shopkeeperTranslateY = (name) => {
    switch (name) {
        case "Nagao":
            return "translate-y-28 -translate-x-2";
        case "Goda":
            return "translate-y-36";
        case "Mihara":
            return "translate-y-32 translate-x-4";
        case "Shoko":
            return "translate-y-36 translate-x-4";
        case "Otake":
            return "translate-y-36 translate-x-2";
        case "Honda":
            return "translate-y-36 translate-x-4";
        default:
            return "translate-y-36";
    }
};

export const shopkeeperImageSmall = (name) => {
    const basePath = import.meta.env.VITE_IMAGE_CDN_URL + `/static/characters`;
    const capitalisedName = capitaliseFirstLetter(name);
    switch (capitalisedName) {
        case "Nagao":
            return basePath + "/Nagao/happyopenSmall.webp";
        case "Goda":
            return basePath + "/Goda/happyopenSmall.webp";
        case "Mihara":
            return basePath + "/Mihara/happySmall.webp";
        case "Shoko":
            return basePath + "/Shoko/happyopenSmall.webp";
        case "Otake":
            return basePath + "/Otake/neutralSmall.webp";
        case "Honda":
            return basePath + "/Honda/neutralSmall.webp";
        default:
            return "";
    }
};

function SingleShopkeeper({ singleShop, setSellOrBuyTab, sellOrBuyTab, cash }) {
    const isMobile = useCheckMobileScreen();

    const currentTab = (tabname) => {
        if (sellOrBuyTab === tabname) {
            return true;
        } else {
            return false;
        }
    };

    const buyOrSellTabs = [
        { name: "Buy", current: currentTab("Buy") },
        { name: "Sell", current: currentTab("Sell") },
    ];

    return (
        <div className="relative mb-0 flex flex-col bg-gray-200 md:mt-14 md:rounded-l-md dark:border-gray-600 dark:border-b dark:border-l dark:bg-gray-800">
            <div className="hidden sm:block">
                <Link to={-1}>
                    <button
                        type="button"
                        className="-top-12 -left-8 absolute z-20 mx-3 mt-3 hidden items-center rounded-md border border-transparent bg-blue-600 px-6 py-1.5 text-md text-white shadow-xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 md:mx-8 md:mt-0 md:inline-flex dark:text-stroke-sm"
                    >
                        <ArrowLeft className="mr-1 size-3.5" />
                        Back
                    </button>
                </Link>
                <nav
                    className="relative z-0 flex divide-x divide-gray-200 overflow-hidden rounded-tl-lg border-slate-300 border-t shadow-sm dark:divide-gray-600 dark:border-gray-600 dark:border-l-0"
                    aria-label="Tabs"
                >
                    {buyOrSellTabs.map((tab, tabIdx) => (
                        <button
                            key={tab.name}
                            data-testid={tab.name === "Buy" ? "buy-item-button" : "sell-item-button"}
                            aria-current={tab.current ? "page" : undefined}
                            className={cn(
                                tab.current ? "text-gray-900" : "text-gray-500",
                                tabIdx === 0 ? "rounded-tl-lg" : "",
                                tabIdx === buyOrSellTabs.length - 1 ? "" : "",
                                "group relative min-w-0 flex-1 overflow-hidden bg-white px-4 py-3 text-center font-medium text-lg focus:z-10 dark:bg-gray-900 dark:text-white"
                            )}
                            onClick={() => {
                                setSellOrBuyTab(tab.name);
                            }}
                        >
                            <span>{tab.name}</span>
                            <span
                                aria-hidden="true"
                                className={cn(
                                    tab.current ? "bg-indigo-500" : "bg-transparent",
                                    "absolute inset-x-0 bottom-0 h-[0.15rem]"
                                )}
                            />
                        </button>
                    ))}
                </nav>
            </div>

            <div className="mb-1 flex bg-gray-400 px-3 py-2 text-shadow shadow-sm dark:bg-slate-700">
                <h2 className="mx-auto text-2xl text-white dark:text-slate-200 dark:text-stroke-s-md">
                    {singleShop.name}'s {capitaliseFirstLetter(singleShop.shopType).replace("Furniture", "Sunday")} Shop
                </h2>
            </div>
            <div className="mx-auto my-2 flex flex-row rounded-xl bg-gray-300 px-2 py-1 ring-2 ring-gray-500 dark:border dark:border-black dark:bg-gray-600">
                <TraderRep heartWidth="w-6" shopId={singleShop.id} />
            </div>

            <img
                className={`mx-auto mb-3 w-32 md:mb-0 md:w-1/2`}
                src={displayMissingIcon(shopkeeperImageSmall(singleShop.name))}
                alt=""
            />
            <div className="absolute bottom-2 left-2 flex flex-row text-center text-gray-200 md:hidden">
                <img className="mr-1 h-6 w-auto" src={yenImg} alt="yen" />
                <span className={cn("my-auto", cash.length > 6 ? "text-sm" : "text-lg")}>{cash}</span>
            </div>
            <Link to={-1}>
                <button
                    type="button"
                    className="-left-1 absolute top-11 mx-3 mt-3 inline-flex items-center rounded-md border border-transparent bg-blue-600 px-6 py-1.5 text-md text-white shadow-xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 md:mx-8 md:mt-0 md:hidden dark:text-stroke-sm"
                >
                    <ArrowLeft className="mr-1 size-3.5" />
                    Back
                </button>
            </Link>
            <Link to="/tasks">
                <div className="absolute top-12 right-2 flex items-center justify-center text-center md:static">
                    <Button
                        height="h-10! md:h-12!"
                        className="mt-2! mb-3! md:mt-12! text-base! font-medium! w-24 text-white md:w-48 dark:text-stroke-sm"
                        variant="primary"
                    >
                        Tasks
                    </Button>
                </div>
            </Link>
        </div>
    );
}

export default SingleShopkeeper;
