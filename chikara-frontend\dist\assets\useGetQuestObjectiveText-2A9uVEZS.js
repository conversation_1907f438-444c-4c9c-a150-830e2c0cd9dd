import{b as n,c as _,P as T}from"./index-UBkgY7aq.js";const r=(t={})=>n(_.dailyQuest.getDailyQuests.queryOptions({staleTime:15e3,...t})),a={DEFEAT_NPC:"DEFEAT_NPC",DEFEAT_NPC_IN_TURNS:"DEFEAT_NPC_IN_TURNS",DEFEAT_NPC_WITH_LOW_DAMAGE:"DEFEAT_NPC_WITH_LOW_DAMAGE",DEFEAT_PLAYER:"DEFEAT_PLAYER",PVP_POST_BATTLE_CHOICE:"PVP_POST_BATTLE_CHOICE",ACQUIRE_ITEM:"ACQUIRE_ITEM",CRAFT_ITEM:"CRAFT_ITEM",DELIVER_ITEM:"DELIVER_ITEM",PLACE_BOUNTY:"PLACE_BOUNTY",UNIQUE_OBJECTIVE:"UNIQUE_OBJECTIVE",COMPLETE_MISSIONS:"COMPLETE_MISSIONS",DONATE_TO_SHRINE:"DONATE_TO_SHRINE",DEFEAT_BOSS:"DEFEAT_BOSS",VOTE_ON_SUGGESTION:"VOTE_ON_SUGGESTION",CHARACTER_ENCOUNTERS:"CHARACTER_ENCOUNTERS",WIN_BATTLE:"WIN_BATTLE",COLLECT_BOUNTY_REWARD:"COLLECT_BOUNTY_REWARD",TRAIN_STATS:"TRAIN_STATS",GAMBLING_SLOTS:"GAMBLING_SLOTS",DEFEAT_PLAYER_XNAME:"DEFEAT_PLAYER_XNAME",DEFEAT_SPECIFIC_PLAYER:"DEFEAT_SPECIFIC_PLAYER",GATHER_RESOURCES:"GATHER_RESOURCES",COMPLETE_STORY_EPISODE:"COMPLETE_STORY_EPISODE"},l={[a.TRAIN_STATS]:"Train {quantity} Stat Points",[a.GAMBLING_SLOTS]:"Gamble ¥{quantity}",[a.DEFEAT_SPECIFIC_PLAYER]:"Defeat player #{target} {quantity} Time{pluralSuffix}",[a.DEFEAT_NPC]:"Defeat {quantity} NPC{pluralSuffix}",[a.PVP_POST_BATTLE_CHOICE]:"'{targetAction}' {quantity} player{pluralSuffix} in PvP",[a.COMPLETE_MISSIONS]:"Complete {quantity} Mission{pluralSuffix}",[a.DEFEAT_NPC_WITH_LOW_DAMAGE]:"Defeat NPCs while taking <{target}% Damage",[a.DEFEAT_PLAYER_XNAME]:"Beat players who have an '{targetAction}' in their name",[a.VOTE_ON_SUGGESTION]:"Vote on {quantity} Suggestion{pluralSuffix}",[a.CHARACTER_ENCOUNTERS]:"Complete {quantity} Character encounter{pluralSuffix}",[a.DONATE_TO_SHRINE]:"Donate to the Shrine",[a.DEFEAT_NPC_IN_TURNS]:"Defeat {quantity} NPC{pluralSuffix} in exactly {target} Turns",[a.DEFEAT_BOSS]:"Defeat {quantity} NPC Bosses",[a.WIN_BATTLE]:"Win {quantity} Battle{pluralSuffix}",[a.DEFEAT_PLAYER]:"Win {quantity} PvP Battle{pluralSuffix}",[a.COLLECT_BOUNTY_REWARD]:"Claim {quantity} Bounty{pluralSuffix}",[a.ACQUIRE_ITEM]:"Find {quantity} {targetItemName} in the {location}",[a.CRAFT_ITEM]:"Craft {quantity} {targetItemName}",[a.DELIVER_ITEM]:"Hand in {quantity} {targetItemName}",[a.GATHER_RESOURCES]:"Gather {quantity} {targetItemName} through {targetAction}",[a.PLACE_BOUNTY]:"Place a bounty worth at least ¥{target}",[a.UNIQUE_OBJECTIVE]:"",[a.COMPLETE_STORY_EPISODE]:"Complete the story episode at {location}"},o=t=>{let e=l[t.objectiveType]||t.objectiveType||"Complete the objective";if(e.includes("{quantity}")&&(e=e.replace("{quantity}",t.quantity===null||t.quantity===void 0?"-":t.quantity.toString())),e.includes("{location}")&&(e=e.replace("{location}",t.location===null||t.location===void 0?"-":T(t.location)||"-")),e.includes("{target}")&&(e=e.replace("{target}",t.target===null||t.target===void 0?"-":t.target.toString())),e.includes("{targetAction}")&&(t.objectiveType===a.GATHER_RESOURCES&&(t.targetAction===null||t.targetAction===void 0)?e=e.replace("{targetAction}","any activity"):e=e.replace("{targetAction}",t.targetAction===null||t.targetAction===void 0?"-":T(t.targetAction)||"-")),e.includes("{targetItemName}"))if(t.objectiveType===a.CRAFT_ITEM&&(!t.item||t.item===null)){const E=(t.quantity===null||t.quantity===void 0?0:t.quantity)===1?"":"s";e=e.replace("{targetItemName}",`Item${E}`)}else if(t.objectiveType===a.GATHER_RESOURCES)if(!t.item||t.item===null){const E=(t.quantity===null||t.quantity===void 0?0:t.quantity)===1?"":"s";t.targetAction==="mining"?e=e.replace("{targetItemName}",`ore${E}`):t.targetAction==="scavenging"?e=e.replace("{targetItemName}",`item${E}`):t.targetAction==="foraging"?e=e.replace("{targetItemName}",`plant${E}`):e=e.replace("{targetItemName}",`resource${E}`)}else e=e.replace("{targetItemName}",t.item.name);else e=e.replace("{targetItemName}",t.item===null||t.item===void 0?"-":t.item.name);if(e.includes("{pluralSuffix}")){const i=t.quantity===null||t.quantity===void 0?0:t.quantity;e=e.replace("{pluralSuffix}",i===1?"":"s")}return e};export{a as Q,o as g,l as o,r as u};
