import{r as N,j as o,a0 as Le,a2 as ut,a1 as Ht,_ as Xt,a3 as Yt,a4 as Wt,H as ge,t as ft,l as P,av as Ut,h as ee,au as Vt,e as mt,g as pt,c as ye,aw as $t,m as ve,G as Ft,af as Gt,ax as qt,o as Ge,ay as Kt,az as Qt,aA as Zt}from"./index-UBkgY7aq.js";import{u as Jt}from"./index-D_6AMQE2.js";import{S as ea}from"./settings-00BiDky4.js";var ta="Label",gt=N.forwardRef((i,e)=>o.jsx(Le.label,{...i,ref:e,onMouseDown:a=>{a.target.closest("button, input, select, textarea")||(i.onMouseDown?.(a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));gt.displayName=ta;var aa=gt,Ne="Switch",[ia,ki]=Xt(Ne),[ra,sa]=ia(Ne),vt=N.forwardRef((i,e)=>{const{__scopeSwitch:a,name:t,checked:s,defaultChecked:r,required:n,disabled:c,value:f="on",onCheckedChange:d,form:l,...h}=i,[m,g]=N.useState(null),v=ut(e,j=>g(j)),x=N.useRef(!1),b=m?l||!!m.closest("form"):!0,[k,E]=Ht({prop:s,defaultProp:r??!1,onChange:d,caller:Ne});return o.jsxs(ra,{scope:a,checked:k,disabled:c,children:[o.jsx(Le.button,{type:"button",role:"switch","aria-checked":k,"aria-required":n,"data-state":wt(k),"data-disabled":c?"":void 0,disabled:c,value:f,...h,ref:v,onClick:Yt(i.onClick,j=>{E(S=>!S),b&&(x.current=j.isPropagationStopped(),x.current||j.stopPropagation())})}),b&&o.jsx(yt,{control:m,bubbles:!x.current,name:t,value:f,checked:k,required:n,disabled:c,form:l,style:{transform:"translateX(-100%)"}})]})});vt.displayName=Ne;var xt="SwitchThumb",bt=N.forwardRef((i,e)=>{const{__scopeSwitch:a,...t}=i,s=sa(xt,a);return o.jsx(Le.span,{"data-state":wt(s.checked),"data-disabled":s.disabled?"":void 0,...t,ref:e})});bt.displayName=xt;var na="SwitchBubbleInput",yt=N.forwardRef(({__scopeSwitch:i,control:e,checked:a,bubbles:t=!0,...s},r)=>{const n=N.useRef(null),c=ut(n,r),f=Jt(a),d=Wt(e);return N.useEffect(()=>{const l=n.current;if(!l)return;const h=window.HTMLInputElement.prototype,g=Object.getOwnPropertyDescriptor(h,"checked").set;if(f!==a&&g){const v=new Event("click",{bubbles:t});g.call(l,a),l.dispatchEvent(v)}},[f,a,t]),o.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...s,tabIndex:-1,ref:c,style:{...s.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});yt.displayName=na;function wt(i){return i?"checked":"unchecked"}var oa=vt,ca=bt;/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const la=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-4 8",key:"7n84p3"}]],ha=ge("at-sign",la);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const da=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],ua=ge("bell",da);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fa=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}],["path",{d:"M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662",key:"154egf"}]],ma=ge("circle-user",fa);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pa=[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]],ga=ge("key",pa);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const va=[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]],xa=ge("layout-grid",va);function ba(){const{data:i,refetch:e}=ft(),[a,t]=N.useState(""),[s,r]=N.useState(""),[n,c]=N.useState(""),f=async d=>{if(d.preventDefault(),a!==s){P.error("The emails do not match!");return}const{data:l,error:h}=await Ut({newEmail:a});l&&!h?(P.success("Changes Saved succesfully!"),t(""),r(""),c(""),e()):(h.message?P.error(h.message):P.error("Error saving changes!"),console.log(h))};return o.jsx(o.Fragment,{children:o.jsxs("form",{className:"divide-y divide-gray-200 lg:col-span-9 dark:divide-gray-600",onSubmit:f,children:[o.jsxs("div",{className:"px-4 py-2 sm:p-6 md:py-6 lg:pb-8",children:[o.jsx("div",{children:o.jsx("h2",{className:"font-medium text-gray-900 text-lg text-stroke-sm leading-6 dark:text-gray-200",children:"Email"})}),o.jsx("div",{className:"mt-6 flex flex-col lg:flex-row",children:o.jsx("div",{className:"grow space-y-6",children:o.jsxs("div",{children:[o.jsx("label",{htmlFor:"username",className:"mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300",children:"Current Email Address"}),o.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:o.jsx("input",{disabled:!0,value:i?.email,type:"text",className:"block w-full min-w-0 grow rounded-md border-gray-300 bg-slate-200 opacity-75 sm:text-sm dark:border-gray-600 dark:bg-gray-600 dark:text-white"})})]})})}),o.jsx("div",{className:"mt-6 flex flex-col lg:flex-row",children:o.jsx("div",{className:"grow space-y-6",children:o.jsxs("div",{children:[o.jsx("label",{htmlFor:"username",className:"mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300",children:"New Email Address"}),o.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:o.jsx("input",{value:a,type:"email",name:"email",autoComplete:"email",className:"block w-full min-w-0 grow rounded-md border-gray-300 focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200",onChange:d=>{t(d.target.value)}})})]})})}),o.jsx("div",{className:"mt-6 flex flex-col lg:flex-row",children:o.jsx("div",{className:"grow space-y-6",children:o.jsxs("div",{children:[o.jsx("label",{htmlFor:"username",className:"mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300",children:"Confirm Email Address"}),o.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:o.jsx("input",{value:s,type:"email",name:"confirmEmail",autoComplete:"email",className:"block w-full min-w-0 grow rounded-md border-gray-300 focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200",onChange:d=>{r(d.target.value)}})})]})})})]}),o.jsx("div",{className:"mt-4 flex justify-end p-4 sm:px-6",children:o.jsx("button",{type:"submit",className:"ml-5 inline-flex w-1/5 justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-stroke-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2",children:"Save"})})]})})}const ue=({label:i,description:e,value:a,onChange:t,disabled:s=!1,className:r="pt-4"})=>o.jsxs("div",{className:ee("flex items-center justify-between",r),children:[o.jsxs("div",{className:"flex flex-col",children:[o.jsx(aa,{className:"font-medium text-indigo-500 text-sm dark:text-stroke-sm",children:i}),e&&o.jsx("p",{className:"pb-4 text-gray-500 text-sm dark:text-gray-300",children:e})]}),o.jsx(oa,{disabled:s,checked:a,className:ee(a?"bg-indigo-700 data-disabled:bg-gray-600":"bg-gray-800 data-disabled:bg-gray-600","relative ml-4 inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-black transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-sky-500"),onCheckedChange:t,children:o.jsx(ca,{className:ee(a?"translate-x-5":"translate-x-0","block size-5 rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out data-disabled:bg-gray-600 dark:bg-gray-300")})})]});function ya(){const{hideGlobalChat:i,setHideGlobalChat:e,twelveHrClock:a,setTwelveHrClock:t,colourTheme:s,setColourTheme:r,keyboardShortcutsEnabled:n,setKeyboardShortcutsEnabled:c,zombieEffectsDisabled:f,setZombieEffectsDisabled:d}=Vt();return o.jsx("form",{className:"divide-y divide-gray-200 lg:col-span-9 dark:divide-gray-600",children:o.jsx("div",{className:"divide-y divide-gray-200 dark:divide-gray-600",children:o.jsxs("div",{className:"px-4 sm:px-6",children:[o.jsx("div",{children:o.jsx("h2",{className:"mt-6 font-medium text-gray-900 text-lg leading-6 dark:text-gray-200",children:"Interface"})}),o.jsxs("ul",{className:"mt-2 divide-y divide-gray-200 dark:divide-gray-600",children:[o.jsx(ue,{label:"Enable Combat Keyboard Shortcuts",description:"Enable the use of keyboard shortcuts while in battle.",className:"pt-4",value:n,onChange:c}),o.jsx(ue,{label:"Hide Global Chat",description:"Disable the use of the sidebar global chat.",className:"pt-4",value:i,onChange:e}),o.jsx(ue,{label:"12hr Clock Format",description:"Enable the 12 hour clock time format.",className:"pt-4",value:a,onChange:t}),o.jsx(ue,{label:"Disable Zombie Effects",description:"Disable the visual screen effects when you are a zombie.",className:"pt-4",value:f,onChange:d})]})]})})})}const wa=i=>{const[e,a]=N.useState(i?.pushNotificationsEnabled),t=mt(),s=pt(ye.notifications.updatePushSettings.mutationOptions({onSuccess:()=>{P.success("Saved Successfully"),t.invalidateQueries({queryKey:ye.user.getCurrentUserInfo.key()})},onError:r=>{P.error(`Error: ${r.message}`)}}));return{pushNotificationsEnabled:e,setPushNotificationsEnabled:a,saveNotificationSettings:()=>s.mutate({pushEnabled:e})}};function Na({currentUser:i}){const{pushNotificationsEnabled:e,setPushNotificationsEnabled:a,saveNotificationSettings:t}=wa(i),s=r=>{r.preventDefault(),t()};return o.jsxs("form",{className:"divide-y divide-gray-200 lg:col-span-9 dark:divide-gray-600",onSubmit:s,children:[o.jsxs("div",{className:"px-4 py-2 sm:p-6 md:py-6 lg:pb-8",children:[o.jsx("div",{children:o.jsx("h2",{className:"mt-3 font-medium text-gray-900 text-lg text-stroke-sm leading-6 dark:text-gray-200",children:"Notifications"})}),o.jsx(ue,{label:"Push Notifications",description:"Receive push/web notifications",value:e,onChange:a})]}),o.jsx("div",{className:"mt-4 flex justify-end p-4 sm:px-6",children:o.jsx("button",{type:"submit",className:"ml-5 inline-flex w-1/5 justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-stroke-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2",children:"Save"})})]})}function ka(){const[i,e]=N.useState(""),[a,t]=N.useState(""),[s,r]=N.useState(""),n=async c=>{if(c.preventDefault(),a!==s){P.error("These passwords do not match!");return}if(a===i){P.error("These passwords are the same!");return}const{data:f,error:d}=await $t({newPassword:a,currentPassword:i});f&&!d?(P.success("Changes Saved succesfully!"),e(""),t(""),r("")):(d.message?P.error(d.message):P.error("Error saving changes!"),console.log(d))};return o.jsx(o.Fragment,{children:o.jsxs("form",{className:"divide-y divide-gray-200 lg:col-span-9 dark:divide-gray-600",onSubmit:n,children:[o.jsxs("div",{className:"px-4 py-2 sm:p-6 md:py-6 lg:pb-8",children:[o.jsx("div",{children:o.jsx("h2",{className:"font-medium text-gray-900 text-lg text-stroke-sm leading-6 dark:text-gray-200",children:"Password"})}),o.jsx("div",{className:"mt-6 flex flex-col lg:flex-row",children:o.jsx("div",{className:"grow space-y-6",children:o.jsxs("div",{children:[o.jsx("label",{htmlFor:"username",className:"mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300",children:"Current Password"}),o.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:o.jsx("input",{value:i,type:"password",name:"currentPassword",autoComplete:"password",className:"block w-full min-w-0 grow rounded-md border-gray-300 focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200",onChange:c=>{e(c.target.value)}})})]})})}),o.jsx("div",{className:"mt-6 flex flex-col lg:flex-row",children:o.jsx("div",{className:"grow space-y-6",children:o.jsxs("div",{children:[o.jsx("label",{htmlFor:"username",className:"mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300",children:"New Password"}),o.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:o.jsx("input",{value:a,type:"password",name:"newPassword",autoComplete:"password",className:"block w-full min-w-0 grow rounded-md border-gray-300 focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200",onChange:c=>{t(c.target.value)}})})]})})}),o.jsx("div",{className:"mt-6 flex flex-col lg:flex-row",children:o.jsx("div",{className:"grow space-y-6",children:o.jsxs("div",{children:[o.jsx("label",{htmlFor:"username",className:"mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300",children:"Confirm New Password"}),o.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:o.jsx("input",{value:s,type:"password",name:"confirmPassword",autoComplete:"password",className:"block w-full min-w-0 grow rounded-md border-gray-300 focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200",onChange:c=>{r(c.target.value)}})})]})})})]}),o.jsx("div",{className:"mt-4 flex justify-end p-4 sm:px-6",children:o.jsx("button",{type:"submit",className:"ml-5 inline-flex w-1/5 justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-stroke-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2",children:"Save"})})]})})}const ja="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%2020%2020'%20fill='rgb(156%20163%20175)'%20%3e%3cpath%20fill-rule='evenodd'%20d='M4%203a2%202%200%2000-2%202v10a2%202%200%20002%202h12a2%202%200%20002-2V5a2%202%200%2000-2-2H4zm12%2012H4l4-8%203%206%202-4%203%206z'%20clip-rule='evenodd'%20/%3e%3c/svg%3e",Ca=()=>{const i=mt();return pt(ye.user.updateProfileDetails.mutationOptions({onSuccess:()=>{P.success("Changes saved successfully!"),i.invalidateQueries({queryKey:ye.user.getCurrentUserInfo.key()})},onError:a=>{const t=a.message||"An unknown error occurred";P.error(t)}}))};/*!
 * Cropper.js v1.6.2
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2024-04-21T07:43:05.335Z
 */function qe(i,e){var a=Object.keys(i);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(i);e&&(t=t.filter(function(s){return Object.getOwnPropertyDescriptor(i,s).enumerable})),a.push.apply(a,t)}return a}function Nt(i){for(var e=1;e<arguments.length;e++){var a=arguments[e]!=null?arguments[e]:{};e%2?qe(Object(a),!0).forEach(function(t){Sa(i,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(a)):qe(Object(a)).forEach(function(t){Object.defineProperty(i,t,Object.getOwnPropertyDescriptor(a,t))})}return i}function Ea(i,e){if(typeof i!="object"||!i)return i;var a=i[Symbol.toPrimitive];if(a!==void 0){var t=a.call(i,e);if(typeof t!="object")return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(i)}function kt(i){var e=Ea(i,"string");return typeof e=="symbol"?e:e+""}function Me(i){"@babel/helpers - typeof";return Me=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Me(i)}function Da(i,e){if(!(i instanceof e))throw new TypeError("Cannot call a class as a function")}function Ke(i,e){for(var a=0;a<e.length;a++){var t=e[a];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(i,kt(t.key),t)}}function Ma(i,e,a){return e&&Ke(i.prototype,e),a&&Ke(i,a),Object.defineProperty(i,"prototype",{writable:!1}),i}function Sa(i,e,a){return e=kt(e),e in i?Object.defineProperty(i,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):i[e]=a,i}function jt(i){return Ta(i)||Oa(i)||Aa(i)||Ra()}function Ta(i){if(Array.isArray(i))return Se(i)}function Oa(i){if(typeof Symbol<"u"&&i[Symbol.iterator]!=null||i["@@iterator"]!=null)return Array.from(i)}function Aa(i,e){if(i){if(typeof i=="string")return Se(i,e);var a=Object.prototype.toString.call(i).slice(8,-1);if(a==="Object"&&i.constructor&&(a=i.constructor.name),a==="Map"||a==="Set")return Array.from(i);if(a==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return Se(i,e)}}function Se(i,e){(e==null||e>i.length)&&(e=i.length);for(var a=0,t=new Array(e);a<e;a++)t[a]=i[a];return t}function Ra(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var ke=typeof window<"u"&&typeof window.document<"u",V=ke?window:{},_e=ke&&V.document.documentElement?"ontouchstart"in V.document.documentElement:!1,He=ke?"PointerEvent"in V:!1,D="cropper",Xe="all",Ct="crop",Et="move",Dt="zoom",Q="e",Z="w",ie="s",F="n",ce="ne",le="nw",he="se",de="sw",Te="".concat(D,"-crop"),Qe="".concat(D,"-disabled"),z="".concat(D,"-hidden"),Ze="".concat(D,"-hide"),Pa="".concat(D,"-invisible"),we="".concat(D,"-modal"),Oe="".concat(D,"-move"),me="".concat(D,"Action"),xe="".concat(D,"Preview"),Ye="crop",Mt="move",St="none",Ae="crop",Re="cropend",Pe="cropmove",Ie="cropstart",Je="dblclick",Ia=_e?"touchstart":"mousedown",Ba=_e?"touchmove":"mousemove",za=_e?"touchend touchcancel":"mouseup",et=He?"pointerdown":Ia,tt=He?"pointermove":Ba,at=He?"pointerup pointercancel":za,it="ready",rt="resize",st="wheel",Be="zoom",nt="image/jpeg",La=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,_a=/^data:/,Ha=/^data:image\/jpeg;base64,/,Xa=/^img|canvas$/i,Tt=200,Ot=100,ot={viewMode:0,dragMode:Ye,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:Tt,minContainerHeight:Ot,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},Ya='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>',Wa=Number.isNaN||V.isNaN;function w(i){return typeof i=="number"&&!Wa(i)}var ct=function(e){return e>0&&e<1/0};function Ce(i){return typeof i>"u"}function te(i){return Me(i)==="object"&&i!==null}var Ua=Object.prototype.hasOwnProperty;function re(i){if(!te(i))return!1;try{var e=i.constructor,a=e.prototype;return e&&a&&Ua.call(a,"isPrototypeOf")}catch{return!1}}function B(i){return typeof i=="function"}var Va=Array.prototype.slice;function At(i){return Array.from?Array.from(i):Va.call(i)}function T(i,e){return i&&B(e)&&(Array.isArray(i)||w(i.length)?At(i).forEach(function(a,t){e.call(i,a,t,i)}):te(i)&&Object.keys(i).forEach(function(a){e.call(i,i[a],a,i)})),i}var M=Object.assign||function(e){for(var a=arguments.length,t=new Array(a>1?a-1:0),s=1;s<a;s++)t[s-1]=arguments[s];return te(e)&&t.length>0&&t.forEach(function(r){te(r)&&Object.keys(r).forEach(function(n){e[n]=r[n]})}),e},$a=/\.\d*(?:0|9){12}\d*$/;function ne(i){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e11;return $a.test(i)?Math.round(i*e)/e:i}var Fa=/^width|height|left|top|marginLeft|marginTop$/;function G(i,e){var a=i.style;T(e,function(t,s){Fa.test(s)&&w(t)&&(t="".concat(t,"px")),a[s]=t})}function Ga(i,e){return i.classList?i.classList.contains(e):i.className.indexOf(e)>-1}function O(i,e){if(e){if(w(i.length)){T(i,function(t){O(t,e)});return}if(i.classList){i.classList.add(e);return}var a=i.className.trim();a?a.indexOf(e)<0&&(i.className="".concat(a," ").concat(e)):i.className=e}}function U(i,e){if(e){if(w(i.length)){T(i,function(a){U(a,e)});return}if(i.classList){i.classList.remove(e);return}i.className.indexOf(e)>=0&&(i.className=i.className.replace(e,""))}}function se(i,e,a){if(e){if(w(i.length)){T(i,function(t){se(t,e,a)});return}a?O(i,e):U(i,e)}}var qa=/([a-z\d])([A-Z])/g;function We(i){return i.replace(qa,"$1-$2").toLowerCase()}function ze(i,e){return te(i[e])?i[e]:i.dataset?i.dataset[e]:i.getAttribute("data-".concat(We(e)))}function pe(i,e,a){te(a)?i[e]=a:i.dataset?i.dataset[e]=a:i.setAttribute("data-".concat(We(e)),a)}function Ka(i,e){if(te(i[e]))try{delete i[e]}catch{i[e]=void 0}else if(i.dataset)try{delete i.dataset[e]}catch{i.dataset[e]=void 0}else i.removeAttribute("data-".concat(We(e)))}var Rt=/\s\s*/,Pt=function(){var i=!1;if(ke){var e=!1,a=function(){},t=Object.defineProperty({},"once",{get:function(){return i=!0,e},set:function(r){e=r}});V.addEventListener("test",a,t),V.removeEventListener("test",a,t)}return i}();function H(i,e,a){var t=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},s=a;e.trim().split(Rt).forEach(function(r){if(!Pt){var n=i.listeners;n&&n[r]&&n[r][a]&&(s=n[r][a],delete n[r][a],Object.keys(n[r]).length===0&&delete n[r],Object.keys(n).length===0&&delete i.listeners)}i.removeEventListener(r,s,t)})}function _(i,e,a){var t=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},s=a;e.trim().split(Rt).forEach(function(r){if(t.once&&!Pt){var n=i.listeners,c=n===void 0?{}:n;s=function(){delete c[r][a],i.removeEventListener(r,s,t);for(var d=arguments.length,l=new Array(d),h=0;h<d;h++)l[h]=arguments[h];a.apply(i,l)},c[r]||(c[r]={}),c[r][a]&&i.removeEventListener(r,c[r][a],t),c[r][a]=s,i.listeners=c}i.addEventListener(r,s,t)})}function oe(i,e,a){var t;return B(Event)&&B(CustomEvent)?t=new CustomEvent(e,{detail:a,bubbles:!0,cancelable:!0}):(t=document.createEvent("CustomEvent"),t.initCustomEvent(e,!0,!0,a)),i.dispatchEvent(t)}function It(i){var e=i.getBoundingClientRect();return{left:e.left+(window.pageXOffset-document.documentElement.clientLeft),top:e.top+(window.pageYOffset-document.documentElement.clientTop)}}var Ee=V.location,Qa=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function lt(i){var e=i.match(Qa);return e!==null&&(e[1]!==Ee.protocol||e[2]!==Ee.hostname||e[3]!==Ee.port)}function ht(i){var e="timestamp=".concat(new Date().getTime());return i+(i.indexOf("?")===-1?"?":"&")+e}function fe(i){var e=i.rotate,a=i.scaleX,t=i.scaleY,s=i.translateX,r=i.translateY,n=[];w(s)&&s!==0&&n.push("translateX(".concat(s,"px)")),w(r)&&r!==0&&n.push("translateY(".concat(r,"px)")),w(e)&&e!==0&&n.push("rotate(".concat(e,"deg)")),w(a)&&a!==1&&n.push("scaleX(".concat(a,")")),w(t)&&t!==1&&n.push("scaleY(".concat(t,")"));var c=n.length?n.join(" "):"none";return{WebkitTransform:c,msTransform:c,transform:c}}function Za(i){var e=Nt({},i),a=0;return T(i,function(t,s){delete e[s],T(e,function(r){var n=Math.abs(t.startX-r.startX),c=Math.abs(t.startY-r.startY),f=Math.abs(t.endX-r.endX),d=Math.abs(t.endY-r.endY),l=Math.sqrt(n*n+c*c),h=Math.sqrt(f*f+d*d),m=(h-l)/l;Math.abs(m)>Math.abs(a)&&(a=m)})}),a}function be(i,e){var a=i.pageX,t=i.pageY,s={endX:a,endY:t};return e?s:Nt({startX:a,startY:t},s)}function Ja(i){var e=0,a=0,t=0;return T(i,function(s){var r=s.startX,n=s.startY;e+=r,a+=n,t+=1}),e/=t,a/=t,{pageX:e,pageY:a}}function q(i){var e=i.aspectRatio,a=i.height,t=i.width,s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"contain",r=ct(t),n=ct(a);if(r&&n){var c=a*e;s==="contain"&&c>t||s==="cover"&&c<t?a=t/e:t=a*e}else r?a=t/e:n&&(t=a*e);return{width:t,height:a}}function ei(i){var e=i.width,a=i.height,t=i.degree;if(t=Math.abs(t)%180,t===90)return{width:a,height:e};var s=t%90*Math.PI/180,r=Math.sin(s),n=Math.cos(s),c=e*n+a*r,f=e*r+a*n;return t>90?{width:f,height:c}:{width:c,height:f}}function ti(i,e,a,t){var s=e.aspectRatio,r=e.naturalWidth,n=e.naturalHeight,c=e.rotate,f=c===void 0?0:c,d=e.scaleX,l=d===void 0?1:d,h=e.scaleY,m=h===void 0?1:h,g=a.aspectRatio,v=a.naturalWidth,x=a.naturalHeight,b=t.fillColor,k=b===void 0?"transparent":b,E=t.imageSmoothingEnabled,j=E===void 0?!0:E,S=t.imageSmoothingQuality,A=S===void 0?"low":S,u=t.maxWidth,p=u===void 0?1/0:u,y=t.maxHeight,C=y===void 0?1/0:y,R=t.minWidth,$=R===void 0?0:R,X=t.minHeight,Y=X===void 0?0:X,L=document.createElement("canvas"),I=L.getContext("2d"),W=q({aspectRatio:g,width:p,height:C}),K=q({aspectRatio:g,width:$,height:Y},"cover"),ae=Math.min(W.width,Math.max(K.width,v)),je=Math.min(W.height,Math.max(K.height,x)),Ue=q({aspectRatio:s,width:p,height:C}),Ve=q({aspectRatio:s,width:$,height:Y},"cover"),$e=Math.min(Ue.width,Math.max(Ve.width,r)),Fe=Math.min(Ue.height,Math.max(Ve.height,n)),Lt=[-$e/2,-Fe/2,$e,Fe];return L.width=ne(ae),L.height=ne(je),I.fillStyle=k,I.fillRect(0,0,ae,je),I.save(),I.translate(ae/2,je/2),I.rotate(f*Math.PI/180),I.scale(l,m),I.imageSmoothingEnabled=j,I.imageSmoothingQuality=A,I.drawImage.apply(I,[i].concat(jt(Lt.map(function(_t){return Math.floor(ne(_t))})))),I.restore(),L}var Bt=String.fromCharCode;function ai(i,e,a){var t="";a+=e;for(var s=e;s<a;s+=1)t+=Bt(i.getUint8(s));return t}var ii=/^data:.*,/;function ri(i){var e=i.replace(ii,""),a=atob(e),t=new ArrayBuffer(a.length),s=new Uint8Array(t);return T(s,function(r,n){s[n]=a.charCodeAt(n)}),t}function si(i,e){for(var a=[],t=8192,s=new Uint8Array(i);s.length>0;)a.push(Bt.apply(null,At(s.subarray(0,t)))),s=s.subarray(t);return"data:".concat(e,";base64,").concat(btoa(a.join("")))}function ni(i){var e=new DataView(i),a;try{var t,s,r;if(e.getUint8(0)===255&&e.getUint8(1)===216)for(var n=e.byteLength,c=2;c+1<n;){if(e.getUint8(c)===255&&e.getUint8(c+1)===225){s=c;break}c+=1}if(s){var f=s+4,d=s+10;if(ai(e,f,4)==="Exif"){var l=e.getUint16(d);if(t=l===18761,(t||l===19789)&&e.getUint16(d+2,t)===42){var h=e.getUint32(d+4,t);h>=8&&(r=d+h)}}}if(r){var m=e.getUint16(r,t),g,v;for(v=0;v<m;v+=1)if(g=r+v*12+2,e.getUint16(g,t)===274){g+=8,a=e.getUint16(g,t),e.setUint16(g,1,t);break}}}catch{a=1}return a}function oi(i){var e=0,a=1,t=1;switch(i){case 2:a=-1;break;case 3:e=-180;break;case 4:t=-1;break;case 5:e=90,t=-1;break;case 6:e=90;break;case 7:e=90,a=-1;break;case 8:e=-90;break}return{rotate:e,scaleX:a,scaleY:t}}var ci={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var e=this.element,a=this.options,t=this.container,s=this.cropper,r=Number(a.minContainerWidth),n=Number(a.minContainerHeight);O(s,z),U(e,z);var c={width:Math.max(t.offsetWidth,r>=0?r:Tt),height:Math.max(t.offsetHeight,n>=0?n:Ot)};this.containerData=c,G(s,{width:c.width,height:c.height}),O(e,z),U(s,z)},initCanvas:function(){var e=this.containerData,a=this.imageData,t=this.options.viewMode,s=Math.abs(a.rotate)%180===90,r=s?a.naturalHeight:a.naturalWidth,n=s?a.naturalWidth:a.naturalHeight,c=r/n,f=e.width,d=e.height;e.height*c>e.width?t===3?f=e.height*c:d=e.width/c:t===3?d=e.width/c:f=e.height*c;var l={aspectRatio:c,naturalWidth:r,naturalHeight:n,width:f,height:d};this.canvasData=l,this.limited=t===1||t===2,this.limitCanvas(!0,!0),l.width=Math.min(Math.max(l.width,l.minWidth),l.maxWidth),l.height=Math.min(Math.max(l.height,l.minHeight),l.maxHeight),l.left=(e.width-l.width)/2,l.top=(e.height-l.height)/2,l.oldLeft=l.left,l.oldTop=l.top,this.initialCanvasData=M({},l)},limitCanvas:function(e,a){var t=this.options,s=this.containerData,r=this.canvasData,n=this.cropBoxData,c=t.viewMode,f=r.aspectRatio,d=this.cropped&&n;if(e){var l=Number(t.minCanvasWidth)||0,h=Number(t.minCanvasHeight)||0;c>1?(l=Math.max(l,s.width),h=Math.max(h,s.height),c===3&&(h*f>l?l=h*f:h=l/f)):c>0&&(l?l=Math.max(l,d?n.width:0):h?h=Math.max(h,d?n.height:0):d&&(l=n.width,h=n.height,h*f>l?l=h*f:h=l/f));var m=q({aspectRatio:f,width:l,height:h});l=m.width,h=m.height,r.minWidth=l,r.minHeight=h,r.maxWidth=1/0,r.maxHeight=1/0}if(a)if(c>(d?0:1)){var g=s.width-r.width,v=s.height-r.height;r.minLeft=Math.min(0,g),r.minTop=Math.min(0,v),r.maxLeft=Math.max(0,g),r.maxTop=Math.max(0,v),d&&this.limited&&(r.minLeft=Math.min(n.left,n.left+(n.width-r.width)),r.minTop=Math.min(n.top,n.top+(n.height-r.height)),r.maxLeft=n.left,r.maxTop=n.top,c===2&&(r.width>=s.width&&(r.minLeft=Math.min(0,g),r.maxLeft=Math.max(0,g)),r.height>=s.height&&(r.minTop=Math.min(0,v),r.maxTop=Math.max(0,v))))}else r.minLeft=-r.width,r.minTop=-r.height,r.maxLeft=s.width,r.maxTop=s.height},renderCanvas:function(e,a){var t=this.canvasData,s=this.imageData;if(a){var r=ei({width:s.naturalWidth*Math.abs(s.scaleX||1),height:s.naturalHeight*Math.abs(s.scaleY||1),degree:s.rotate||0}),n=r.width,c=r.height,f=t.width*(n/t.naturalWidth),d=t.height*(c/t.naturalHeight);t.left-=(f-t.width)/2,t.top-=(d-t.height)/2,t.width=f,t.height=d,t.aspectRatio=n/c,t.naturalWidth=n,t.naturalHeight=c,this.limitCanvas(!0,!1)}(t.width>t.maxWidth||t.width<t.minWidth)&&(t.left=t.oldLeft),(t.height>t.maxHeight||t.height<t.minHeight)&&(t.top=t.oldTop),t.width=Math.min(Math.max(t.width,t.minWidth),t.maxWidth),t.height=Math.min(Math.max(t.height,t.minHeight),t.maxHeight),this.limitCanvas(!1,!0),t.left=Math.min(Math.max(t.left,t.minLeft),t.maxLeft),t.top=Math.min(Math.max(t.top,t.minTop),t.maxTop),t.oldLeft=t.left,t.oldTop=t.top,G(this.canvas,M({width:t.width,height:t.height},fe({translateX:t.left,translateY:t.top}))),this.renderImage(e),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(e){var a=this.canvasData,t=this.imageData,s=t.naturalWidth*(a.width/a.naturalWidth),r=t.naturalHeight*(a.height/a.naturalHeight);M(t,{width:s,height:r,left:(a.width-s)/2,top:(a.height-r)/2}),G(this.image,M({width:t.width,height:t.height},fe(M({translateX:t.left,translateY:t.top},t)))),e&&this.output()},initCropBox:function(){var e=this.options,a=this.canvasData,t=e.aspectRatio||e.initialAspectRatio,s=Number(e.autoCropArea)||.8,r={width:a.width,height:a.height};t&&(a.height*t>a.width?r.height=r.width/t:r.width=r.height*t),this.cropBoxData=r,this.limitCropBox(!0,!0),r.width=Math.min(Math.max(r.width,r.minWidth),r.maxWidth),r.height=Math.min(Math.max(r.height,r.minHeight),r.maxHeight),r.width=Math.max(r.minWidth,r.width*s),r.height=Math.max(r.minHeight,r.height*s),r.left=a.left+(a.width-r.width)/2,r.top=a.top+(a.height-r.height)/2,r.oldLeft=r.left,r.oldTop=r.top,this.initialCropBoxData=M({},r)},limitCropBox:function(e,a){var t=this.options,s=this.containerData,r=this.canvasData,n=this.cropBoxData,c=this.limited,f=t.aspectRatio;if(e){var d=Number(t.minCropBoxWidth)||0,l=Number(t.minCropBoxHeight)||0,h=c?Math.min(s.width,r.width,r.width+r.left,s.width-r.left):s.width,m=c?Math.min(s.height,r.height,r.height+r.top,s.height-r.top):s.height;d=Math.min(d,s.width),l=Math.min(l,s.height),f&&(d&&l?l*f>d?l=d/f:d=l*f:d?l=d/f:l&&(d=l*f),m*f>h?m=h/f:h=m*f),n.minWidth=Math.min(d,h),n.minHeight=Math.min(l,m),n.maxWidth=h,n.maxHeight=m}a&&(c?(n.minLeft=Math.max(0,r.left),n.minTop=Math.max(0,r.top),n.maxLeft=Math.min(s.width,r.left+r.width)-n.width,n.maxTop=Math.min(s.height,r.top+r.height)-n.height):(n.minLeft=0,n.minTop=0,n.maxLeft=s.width-n.width,n.maxTop=s.height-n.height))},renderCropBox:function(){var e=this.options,a=this.containerData,t=this.cropBoxData;(t.width>t.maxWidth||t.width<t.minWidth)&&(t.left=t.oldLeft),(t.height>t.maxHeight||t.height<t.minHeight)&&(t.top=t.oldTop),t.width=Math.min(Math.max(t.width,t.minWidth),t.maxWidth),t.height=Math.min(Math.max(t.height,t.minHeight),t.maxHeight),this.limitCropBox(!1,!0),t.left=Math.min(Math.max(t.left,t.minLeft),t.maxLeft),t.top=Math.min(Math.max(t.top,t.minTop),t.maxTop),t.oldLeft=t.left,t.oldTop=t.top,e.movable&&e.cropBoxMovable&&pe(this.face,me,t.width>=a.width&&t.height>=a.height?Et:Xe),G(this.cropBox,M({width:t.width,height:t.height},fe({translateX:t.left,translateY:t.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),oe(this.element,Ae,this.getData())}},li={initPreview:function(){var e=this.element,a=this.crossOrigin,t=this.options.preview,s=a?this.crossOriginUrl:this.url,r=e.alt||"The image to preview",n=document.createElement("img");if(a&&(n.crossOrigin=a),n.src=s,n.alt=r,this.viewBox.appendChild(n),this.viewBoxImage=n,!!t){var c=t;typeof t=="string"?c=e.ownerDocument.querySelectorAll(t):t.querySelector&&(c=[t]),this.previews=c,T(c,function(f){var d=document.createElement("img");pe(f,xe,{width:f.offsetWidth,height:f.offsetHeight,html:f.innerHTML}),a&&(d.crossOrigin=a),d.src=s,d.alt=r,d.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',f.innerHTML="",f.appendChild(d)})}},resetPreview:function(){T(this.previews,function(e){var a=ze(e,xe);G(e,{width:a.width,height:a.height}),e.innerHTML=a.html,Ka(e,xe)})},preview:function(){var e=this.imageData,a=this.canvasData,t=this.cropBoxData,s=t.width,r=t.height,n=e.width,c=e.height,f=t.left-a.left-e.left,d=t.top-a.top-e.top;!this.cropped||this.disabled||(G(this.viewBoxImage,M({width:n,height:c},fe(M({translateX:-f,translateY:-d},e)))),T(this.previews,function(l){var h=ze(l,xe),m=h.width,g=h.height,v=m,x=g,b=1;s&&(b=m/s,x=r*b),r&&x>g&&(b=g/r,v=s*b,x=g),G(l,{width:v,height:x}),G(l.getElementsByTagName("img")[0],M({width:n*b,height:c*b},fe(M({translateX:-f*b,translateY:-d*b},e))))}))}},hi={bind:function(){var e=this.element,a=this.options,t=this.cropper;B(a.cropstart)&&_(e,Ie,a.cropstart),B(a.cropmove)&&_(e,Pe,a.cropmove),B(a.cropend)&&_(e,Re,a.cropend),B(a.crop)&&_(e,Ae,a.crop),B(a.zoom)&&_(e,Be,a.zoom),_(t,et,this.onCropStart=this.cropStart.bind(this)),a.zoomable&&a.zoomOnWheel&&_(t,st,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),a.toggleDragModeOnDblclick&&_(t,Je,this.onDblclick=this.dblclick.bind(this)),_(e.ownerDocument,tt,this.onCropMove=this.cropMove.bind(this)),_(e.ownerDocument,at,this.onCropEnd=this.cropEnd.bind(this)),a.responsive&&_(window,rt,this.onResize=this.resize.bind(this))},unbind:function(){var e=this.element,a=this.options,t=this.cropper;B(a.cropstart)&&H(e,Ie,a.cropstart),B(a.cropmove)&&H(e,Pe,a.cropmove),B(a.cropend)&&H(e,Re,a.cropend),B(a.crop)&&H(e,Ae,a.crop),B(a.zoom)&&H(e,Be,a.zoom),H(t,et,this.onCropStart),a.zoomable&&a.zoomOnWheel&&H(t,st,this.onWheel,{passive:!1,capture:!0}),a.toggleDragModeOnDblclick&&H(t,Je,this.onDblclick),H(e.ownerDocument,tt,this.onCropMove),H(e.ownerDocument,at,this.onCropEnd),a.responsive&&H(window,rt,this.onResize)}},di={resize:function(){if(!this.disabled){var e=this.options,a=this.container,t=this.containerData,s=a.offsetWidth/t.width,r=a.offsetHeight/t.height,n=Math.abs(s-1)>Math.abs(r-1)?s:r;if(n!==1){var c,f;e.restore&&(c=this.getCanvasData(),f=this.getCropBoxData()),this.render(),e.restore&&(this.setCanvasData(T(c,function(d,l){c[l]=d*n})),this.setCropBoxData(T(f,function(d,l){f[l]=d*n})))}}},dblclick:function(){this.disabled||this.options.dragMode===St||this.setDragMode(Ga(this.dragBox,Te)?Mt:Ye)},wheel:function(e){var a=this,t=Number(this.options.wheelZoomRatio)||.1,s=1;this.disabled||(e.preventDefault(),!this.wheeling&&(this.wheeling=!0,setTimeout(function(){a.wheeling=!1},50),e.deltaY?s=e.deltaY>0?1:-1:e.wheelDelta?s=-e.wheelDelta/120:e.detail&&(s=e.detail>0?1:-1),this.zoom(-s*t,e)))},cropStart:function(e){var a=e.buttons,t=e.button;if(!(this.disabled||(e.type==="mousedown"||e.type==="pointerdown"&&e.pointerType==="mouse")&&(w(a)&&a!==1||w(t)&&t!==0||e.ctrlKey))){var s=this.options,r=this.pointers,n;e.changedTouches?T(e.changedTouches,function(c){r[c.identifier]=be(c)}):r[e.pointerId||0]=be(e),Object.keys(r).length>1&&s.zoomable&&s.zoomOnTouch?n=Dt:n=ze(e.target,me),La.test(n)&&oe(this.element,Ie,{originalEvent:e,action:n})!==!1&&(e.preventDefault(),this.action=n,this.cropping=!1,n===Ct&&(this.cropping=!0,O(this.dragBox,we)))}},cropMove:function(e){var a=this.action;if(!(this.disabled||!a)){var t=this.pointers;e.preventDefault(),oe(this.element,Pe,{originalEvent:e,action:a})!==!1&&(e.changedTouches?T(e.changedTouches,function(s){M(t[s.identifier]||{},be(s,!0))}):M(t[e.pointerId||0]||{},be(e,!0)),this.change(e))}},cropEnd:function(e){if(!this.disabled){var a=this.action,t=this.pointers;e.changedTouches?T(e.changedTouches,function(s){delete t[s.identifier]}):delete t[e.pointerId||0],a&&(e.preventDefault(),Object.keys(t).length||(this.action=""),this.cropping&&(this.cropping=!1,se(this.dragBox,we,this.cropped&&this.options.modal)),oe(this.element,Re,{originalEvent:e,action:a}))}}},ui={change:function(e){var a=this.options,t=this.canvasData,s=this.containerData,r=this.cropBoxData,n=this.pointers,c=this.action,f=a.aspectRatio,d=r.left,l=r.top,h=r.width,m=r.height,g=d+h,v=l+m,x=0,b=0,k=s.width,E=s.height,j=!0,S;!f&&e.shiftKey&&(f=h&&m?h/m:1),this.limited&&(x=r.minLeft,b=r.minTop,k=x+Math.min(s.width,t.width,t.left+t.width),E=b+Math.min(s.height,t.height,t.top+t.height));var A=n[Object.keys(n)[0]],u={x:A.endX-A.startX,y:A.endY-A.startY},p=function(C){switch(C){case Q:g+u.x>k&&(u.x=k-g);break;case Z:d+u.x<x&&(u.x=x-d);break;case F:l+u.y<b&&(u.y=b-l);break;case ie:v+u.y>E&&(u.y=E-v);break}};switch(c){case Xe:d+=u.x,l+=u.y;break;case Q:if(u.x>=0&&(g>=k||f&&(l<=b||v>=E))){j=!1;break}p(Q),h+=u.x,h<0&&(c=Z,h=-h,d-=h),f&&(m=h/f,l+=(r.height-m)/2);break;case F:if(u.y<=0&&(l<=b||f&&(d<=x||g>=k))){j=!1;break}p(F),m-=u.y,l+=u.y,m<0&&(c=ie,m=-m,l-=m),f&&(h=m*f,d+=(r.width-h)/2);break;case Z:if(u.x<=0&&(d<=x||f&&(l<=b||v>=E))){j=!1;break}p(Z),h-=u.x,d+=u.x,h<0&&(c=Q,h=-h,d-=h),f&&(m=h/f,l+=(r.height-m)/2);break;case ie:if(u.y>=0&&(v>=E||f&&(d<=x||g>=k))){j=!1;break}p(ie),m+=u.y,m<0&&(c=F,m=-m,l-=m),f&&(h=m*f,d+=(r.width-h)/2);break;case ce:if(f){if(u.y<=0&&(l<=b||g>=k)){j=!1;break}p(F),m-=u.y,l+=u.y,h=m*f}else p(F),p(Q),u.x>=0?g<k?h+=u.x:u.y<=0&&l<=b&&(j=!1):h+=u.x,u.y<=0?l>b&&(m-=u.y,l+=u.y):(m-=u.y,l+=u.y);h<0&&m<0?(c=de,m=-m,h=-h,l-=m,d-=h):h<0?(c=le,h=-h,d-=h):m<0&&(c=he,m=-m,l-=m);break;case le:if(f){if(u.y<=0&&(l<=b||d<=x)){j=!1;break}p(F),m-=u.y,l+=u.y,h=m*f,d+=r.width-h}else p(F),p(Z),u.x<=0?d>x?(h-=u.x,d+=u.x):u.y<=0&&l<=b&&(j=!1):(h-=u.x,d+=u.x),u.y<=0?l>b&&(m-=u.y,l+=u.y):(m-=u.y,l+=u.y);h<0&&m<0?(c=he,m=-m,h=-h,l-=m,d-=h):h<0?(c=ce,h=-h,d-=h):m<0&&(c=de,m=-m,l-=m);break;case de:if(f){if(u.x<=0&&(d<=x||v>=E)){j=!1;break}p(Z),h-=u.x,d+=u.x,m=h/f}else p(ie),p(Z),u.x<=0?d>x?(h-=u.x,d+=u.x):u.y>=0&&v>=E&&(j=!1):(h-=u.x,d+=u.x),u.y>=0?v<E&&(m+=u.y):m+=u.y;h<0&&m<0?(c=ce,m=-m,h=-h,l-=m,d-=h):h<0?(c=he,h=-h,d-=h):m<0&&(c=le,m=-m,l-=m);break;case he:if(f){if(u.x>=0&&(g>=k||v>=E)){j=!1;break}p(Q),h+=u.x,m=h/f}else p(ie),p(Q),u.x>=0?g<k?h+=u.x:u.y>=0&&v>=E&&(j=!1):h+=u.x,u.y>=0?v<E&&(m+=u.y):m+=u.y;h<0&&m<0?(c=le,m=-m,h=-h,l-=m,d-=h):h<0?(c=de,h=-h,d-=h):m<0&&(c=ce,m=-m,l-=m);break;case Et:this.move(u.x,u.y),j=!1;break;case Dt:this.zoom(Za(n),e),j=!1;break;case Ct:if(!u.x||!u.y){j=!1;break}S=It(this.cropper),d=A.startX-S.left,l=A.startY-S.top,h=r.minWidth,m=r.minHeight,u.x>0?c=u.y>0?he:ce:u.x<0&&(d-=h,c=u.y>0?de:le),u.y<0&&(l-=m),this.cropped||(U(this.cropBox,z),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0));break}j&&(r.width=h,r.height=m,r.left=d,r.top=l,this.action=c,this.renderCropBox()),T(n,function(y){y.startX=y.endX,y.startY=y.endY})}},fi={crop:function(){return this.ready&&!this.cropped&&!this.disabled&&(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&O(this.dragBox,we),U(this.cropBox,z),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=M({},this.initialImageData),this.canvasData=M({},this.initialCanvasData),this.cropBoxData=M({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(M(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),U(this.dragBox,we),O(this.cropBox,z)),this},replace:function(e){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return!this.disabled&&e&&(this.isImg&&(this.element.src=e),a?(this.url=e,this.image.src=e,this.ready&&(this.viewBoxImage.src=e,T(this.previews,function(t){t.getElementsByTagName("img")[0].src=e}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(e))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,U(this.cropper,Qe)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,O(this.cropper,Qe)),this},destroy:function(){var e=this.element;return e[D]?(e[D]=void 0,this.isImg&&this.replaced&&(e.src=this.originalUrl),this.uncreate(),this):this},move:function(e){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,t=this.canvasData,s=t.left,r=t.top;return this.moveTo(Ce(e)?e:s+Number(e),Ce(a)?a:r+Number(a))},moveTo:function(e){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,t=this.canvasData,s=!1;return e=Number(e),a=Number(a),this.ready&&!this.disabled&&this.options.movable&&(w(e)&&(t.left=e,s=!0),w(a)&&(t.top=a,s=!0),s&&this.renderCanvas(!0)),this},zoom:function(e,a){var t=this.canvasData;return e=Number(e),e<0?e=1/(1-e):e=1+e,this.zoomTo(t.width*e/t.naturalWidth,null,a)},zoomTo:function(e,a,t){var s=this.options,r=this.canvasData,n=r.width,c=r.height,f=r.naturalWidth,d=r.naturalHeight;if(e=Number(e),e>=0&&this.ready&&!this.disabled&&s.zoomable){var l=f*e,h=d*e;if(oe(this.element,Be,{ratio:e,oldRatio:n/f,originalEvent:t})===!1)return this;if(t){var m=this.pointers,g=It(this.cropper),v=m&&Object.keys(m).length?Ja(m):{pageX:t.pageX,pageY:t.pageY};r.left-=(l-n)*((v.pageX-g.left-r.left)/n),r.top-=(h-c)*((v.pageY-g.top-r.top)/c)}else re(a)&&w(a.x)&&w(a.y)?(r.left-=(l-n)*((a.x-r.left)/n),r.top-=(h-c)*((a.y-r.top)/c)):(r.left-=(l-n)/2,r.top-=(h-c)/2);r.width=l,r.height=h,this.renderCanvas(!0)}return this},rotate:function(e){return this.rotateTo((this.imageData.rotate||0)+Number(e))},rotateTo:function(e){return e=Number(e),w(e)&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=e%360,this.renderCanvas(!0,!0)),this},scaleX:function(e){var a=this.imageData.scaleY;return this.scale(e,w(a)?a:1)},scaleY:function(e){var a=this.imageData.scaleX;return this.scale(w(a)?a:1,e)},scale:function(e){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,t=this.imageData,s=!1;return e=Number(e),a=Number(a),this.ready&&!this.disabled&&this.options.scalable&&(w(e)&&(t.scaleX=e,s=!0),w(a)&&(t.scaleY=a,s=!0),s&&this.renderCanvas(!0,!0)),this},getData:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,a=this.options,t=this.imageData,s=this.canvasData,r=this.cropBoxData,n;if(this.ready&&this.cropped){n={x:r.left-s.left,y:r.top-s.top,width:r.width,height:r.height};var c=t.width/t.naturalWidth;if(T(n,function(l,h){n[h]=l/c}),e){var f=Math.round(n.y+n.height),d=Math.round(n.x+n.width);n.x=Math.round(n.x),n.y=Math.round(n.y),n.width=d-n.x,n.height=f-n.y}}else n={x:0,y:0,width:0,height:0};return a.rotatable&&(n.rotate=t.rotate||0),a.scalable&&(n.scaleX=t.scaleX||1,n.scaleY=t.scaleY||1),n},setData:function(e){var a=this.options,t=this.imageData,s=this.canvasData,r={};if(this.ready&&!this.disabled&&re(e)){var n=!1;a.rotatable&&w(e.rotate)&&e.rotate!==t.rotate&&(t.rotate=e.rotate,n=!0),a.scalable&&(w(e.scaleX)&&e.scaleX!==t.scaleX&&(t.scaleX=e.scaleX,n=!0),w(e.scaleY)&&e.scaleY!==t.scaleY&&(t.scaleY=e.scaleY,n=!0)),n&&this.renderCanvas(!0,!0);var c=t.width/t.naturalWidth;w(e.x)&&(r.left=e.x*c+s.left),w(e.y)&&(r.top=e.y*c+s.top),w(e.width)&&(r.width=e.width*c),w(e.height)&&(r.height=e.height*c),this.setCropBoxData(r)}return this},getContainerData:function(){return this.ready?M({},this.containerData):{}},getImageData:function(){return this.sized?M({},this.imageData):{}},getCanvasData:function(){var e=this.canvasData,a={};return this.ready&&T(["left","top","width","height","naturalWidth","naturalHeight"],function(t){a[t]=e[t]}),a},setCanvasData:function(e){var a=this.canvasData,t=a.aspectRatio;return this.ready&&!this.disabled&&re(e)&&(w(e.left)&&(a.left=e.left),w(e.top)&&(a.top=e.top),w(e.width)?(a.width=e.width,a.height=e.width/t):w(e.height)&&(a.height=e.height,a.width=e.height*t),this.renderCanvas(!0)),this},getCropBoxData:function(){var e=this.cropBoxData,a;return this.ready&&this.cropped&&(a={left:e.left,top:e.top,width:e.width,height:e.height}),a||{}},setCropBoxData:function(e){var a=this.cropBoxData,t=this.options.aspectRatio,s,r;return this.ready&&this.cropped&&!this.disabled&&re(e)&&(w(e.left)&&(a.left=e.left),w(e.top)&&(a.top=e.top),w(e.width)&&e.width!==a.width&&(s=!0,a.width=e.width),w(e.height)&&e.height!==a.height&&(r=!0,a.height=e.height),t&&(s?a.height=a.width/t:r&&(a.width=a.height*t)),this.renderCropBox()),this},getCroppedCanvas:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var a=this.canvasData,t=ti(this.image,this.imageData,a,e);if(!this.cropped)return t;var s=this.getData(e.rounded),r=s.x,n=s.y,c=s.width,f=s.height,d=t.width/Math.floor(a.naturalWidth);d!==1&&(r*=d,n*=d,c*=d,f*=d);var l=c/f,h=q({aspectRatio:l,width:e.maxWidth||1/0,height:e.maxHeight||1/0}),m=q({aspectRatio:l,width:e.minWidth||0,height:e.minHeight||0},"cover"),g=q({aspectRatio:l,width:e.width||(d!==1?t.width:c),height:e.height||(d!==1?t.height:f)}),v=g.width,x=g.height;v=Math.min(h.width,Math.max(m.width,v)),x=Math.min(h.height,Math.max(m.height,x));var b=document.createElement("canvas"),k=b.getContext("2d");b.width=ne(v),b.height=ne(x),k.fillStyle=e.fillColor||"transparent",k.fillRect(0,0,v,x);var E=e.imageSmoothingEnabled,j=E===void 0?!0:E,S=e.imageSmoothingQuality;k.imageSmoothingEnabled=j,S&&(k.imageSmoothingQuality=S);var A=t.width,u=t.height,p=r,y=n,C,R,$,X,Y,L;p<=-c||p>A?(p=0,C=0,$=0,Y=0):p<=0?($=-p,p=0,C=Math.min(A,c+p),Y=C):p<=A&&($=0,C=Math.min(c,A-p),Y=C),C<=0||y<=-f||y>u?(y=0,R=0,X=0,L=0):y<=0?(X=-y,y=0,R=Math.min(u,f+y),L=R):y<=u&&(X=0,R=Math.min(f,u-y),L=R);var I=[p,y,C,R];if(Y>0&&L>0){var W=v/c;I.push($*W,X*W,Y*W,L*W)}return k.drawImage.apply(k,[t].concat(jt(I.map(function(K){return Math.floor(ne(K))})))),b},setAspectRatio:function(e){var a=this.options;return!this.disabled&&!Ce(e)&&(a.aspectRatio=Math.max(0,e)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(e){var a=this.options,t=this.dragBox,s=this.face;if(this.ready&&!this.disabled){var r=e===Ye,n=a.movable&&e===Mt;e=r||n?e:St,a.dragMode=e,pe(t,me,e),se(t,Te,r),se(t,Oe,n),a.cropBoxMovable||(pe(s,me,e),se(s,Te,r),se(s,Oe,n))}return this}},mi=V.Cropper,zt=function(){function i(e){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(Da(this,i),!e||!Xa.test(e.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=e,this.options=M({},ot,re(a)&&a),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return Ma(i,[{key:"init",value:function(){var a=this.element,t=a.tagName.toLowerCase(),s;if(!a[D]){if(a[D]=this,t==="img"){if(this.isImg=!0,s=a.getAttribute("src")||"",this.originalUrl=s,!s)return;s=a.src}else t==="canvas"&&window.HTMLCanvasElement&&(s=a.toDataURL());this.load(s)}}},{key:"load",value:function(a){var t=this;if(a){this.url=a,this.imageData={};var s=this.element,r=this.options;if(!r.rotatable&&!r.scalable&&(r.checkOrientation=!1),!r.checkOrientation||!window.ArrayBuffer){this.clone();return}if(_a.test(a)){Ha.test(a)?this.read(ri(a)):this.clone();return}var n=new XMLHttpRequest,c=this.clone.bind(this);this.reloading=!0,this.xhr=n,n.onabort=c,n.onerror=c,n.ontimeout=c,n.onprogress=function(){n.getResponseHeader("content-type")!==nt&&n.abort()},n.onload=function(){t.read(n.response)},n.onloadend=function(){t.reloading=!1,t.xhr=null},r.checkCrossOrigin&&lt(a)&&s.crossOrigin&&(a=ht(a)),n.open("GET",a,!0),n.responseType="arraybuffer",n.withCredentials=s.crossOrigin==="use-credentials",n.send()}}},{key:"read",value:function(a){var t=this.options,s=this.imageData,r=ni(a),n=0,c=1,f=1;if(r>1){this.url=si(a,nt);var d=oi(r);n=d.rotate,c=d.scaleX,f=d.scaleY}t.rotatable&&(s.rotate=n),t.scalable&&(s.scaleX=c,s.scaleY=f),this.clone()}},{key:"clone",value:function(){var a=this.element,t=this.url,s=a.crossOrigin,r=t;this.options.checkCrossOrigin&&lt(t)&&(s||(s="anonymous"),r=ht(t)),this.crossOrigin=s,this.crossOriginUrl=r;var n=document.createElement("img");s&&(n.crossOrigin=s),n.src=r||t,n.alt=a.alt||"The image to crop",this.image=n,n.onload=this.start.bind(this),n.onerror=this.stop.bind(this),O(n,Ze),a.parentNode.insertBefore(n,a.nextSibling)}},{key:"start",value:function(){var a=this,t=this.image;t.onload=null,t.onerror=null,this.sizing=!0;var s=V.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(V.navigator.userAgent),r=function(d,l){M(a.imageData,{naturalWidth:d,naturalHeight:l,aspectRatio:d/l}),a.initialImageData=M({},a.imageData),a.sizing=!1,a.sized=!0,a.build()};if(t.naturalWidth&&!s){r(t.naturalWidth,t.naturalHeight);return}var n=document.createElement("img"),c=document.body||document.documentElement;this.sizingImage=n,n.onload=function(){r(n.width,n.height),s||c.removeChild(n)},n.src=t.src,s||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",c.appendChild(n))}},{key:"stop",value:function(){var a=this.image;a.onload=null,a.onerror=null,a.parentNode.removeChild(a),this.image=null}},{key:"build",value:function(){if(!(!this.sized||this.ready)){var a=this.element,t=this.options,s=this.image,r=a.parentNode,n=document.createElement("div");n.innerHTML=Ya;var c=n.querySelector(".".concat(D,"-container")),f=c.querySelector(".".concat(D,"-canvas")),d=c.querySelector(".".concat(D,"-drag-box")),l=c.querySelector(".".concat(D,"-crop-box")),h=l.querySelector(".".concat(D,"-face"));this.container=r,this.cropper=c,this.canvas=f,this.dragBox=d,this.cropBox=l,this.viewBox=c.querySelector(".".concat(D,"-view-box")),this.face=h,f.appendChild(s),O(a,z),r.insertBefore(c,a.nextSibling),U(s,Ze),this.initPreview(),this.bind(),t.initialAspectRatio=Math.max(0,t.initialAspectRatio)||NaN,t.aspectRatio=Math.max(0,t.aspectRatio)||NaN,t.viewMode=Math.max(0,Math.min(3,Math.round(t.viewMode)))||0,O(l,z),t.guides||O(l.getElementsByClassName("".concat(D,"-dashed")),z),t.center||O(l.getElementsByClassName("".concat(D,"-center")),z),t.background&&O(c,"".concat(D,"-bg")),t.highlight||O(h,Pa),t.cropBoxMovable&&(O(h,Oe),pe(h,me,Xe)),t.cropBoxResizable||(O(l.getElementsByClassName("".concat(D,"-line")),z),O(l.getElementsByClassName("".concat(D,"-point")),z)),this.render(),this.ready=!0,this.setDragMode(t.dragMode),t.autoCrop&&this.crop(),this.setData(t.data),B(t.ready)&&_(a,it,t.ready,{once:!0}),oe(a,it)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var a=this.cropper.parentNode;a&&a.removeChild(this.cropper),U(this.element,z)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],[{key:"noConflict",value:function(){return window.Cropper=mi,i}},{key:"setDefaults",value:function(a){M(ot,re(a)&&a)}}])}();M(zt.prototype,ci,li,hi,di,ui,fi);var J=function(){return J=Object.assign||function(i){for(var e,a=1,t=arguments.length;a<t;a++)for(var s in e=arguments[a])Object.prototype.hasOwnProperty.call(e,s)&&(i[s]=e[s]);return i},J.apply(this,arguments)};function De(i,e){var a={};for(var t in i)Object.prototype.hasOwnProperty.call(i,t)&&e.indexOf(t)<0&&(a[t]=i[t]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function"){var s=0;for(t=Object.getOwnPropertySymbols(i);s<t.length;s++)e.indexOf(t[s])<0&&Object.prototype.propertyIsEnumerable.call(i,t[s])&&(a[t[s]]=i[t[s]])}return a}var pi=["aspectRatio","autoCrop","autoCropArea","background","center","checkCrossOrigin","checkOrientation","cropBoxMovable","cropBoxResizable","data","dragMode","guides","highlight","initialAspectRatio","minCanvasHeight","minCanvasWidth","minContainerHeight","minContainerWidth","minCropBoxHeight","minCropBoxWidth","modal","movable","preview","responsive","restore","rotatable","scalable","toggleDragModeOnDblclick","viewMode","wheelZoomRatio","zoomOnTouch","zoomOnWheel","zoomable","cropstart","cropmove","cropend","crop","zoom","ready"],gi={opacity:0,maxWidth:"100%"},vi=ve.forwardRef(function(i,e){var a=De(i,[]),t=a.dragMode,s=t===void 0?"crop":t,r=a.src,n=a.style,c=a.className,f=a.crossOrigin,d=a.scaleX,l=a.scaleY,h=a.enable,m=a.zoomTo,g=a.rotateTo,v=a.alt,x=v===void 0?"picture":v,b=a.ready,k=a.onInitialized,E=De(a,["dragMode","src","style","className","crossOrigin","scaleX","scaleY","enable","zoomTo","rotateTo","alt","ready","onInitialized"]),j={scaleY:l,scaleX:d,enable:h,zoomTo:m,rotateTo:g},S=function(){for(var u=[],p=0;p<arguments.length;p++)u[p]=arguments[p];var y=N.useRef(null);return ve.useEffect(function(){u.forEach(function(C){C&&(typeof C=="function"?C(y.current):C.current=y.current)})},[u]),y}(e,N.useRef(null));N.useEffect(function(){var u;!((u=S.current)===null||u===void 0)&&u.cropper&&typeof m=="number"&&S.current.cropper.zoomTo(m)},[a.zoomTo]),N.useEffect(function(){var u;!((u=S.current)===null||u===void 0)&&u.cropper&&r!==void 0&&S.current.cropper.reset().clear().replace(r)},[r]),N.useEffect(function(){if(S.current!==null){var u=new zt(S.current,J(J({dragMode:s},E),{ready:function(p){p.currentTarget!==null&&function(y,C){C===void 0&&(C={});var R=C.enable,$=R===void 0||R,X=C.scaleX,Y=X===void 0?1:X,L=C.scaleY,I=L===void 0?1:L,W=C.zoomTo,K=W===void 0?0:W,ae=C.rotateTo;$?y.enable():y.disable(),y.scaleX(Y),y.scaleY(I),ae!==void 0&&y.rotateTo(ae),K>0&&y.zoomTo(K)}(p.currentTarget.cropper,j),b&&b(p)}}));k&&k(u)}return function(){var p,y;(y=(p=S.current)===null||p===void 0?void 0:p.cropper)===null||y===void 0||y.destroy()}},[S]);var A=function(u){return pi.reduce(function(p,y){var C=p,R=y;return C[R],De(C,[typeof R=="symbol"?R:R+""])},u)}(J(J({},E),{crossOrigin:f,src:r,alt:x}));return ve.createElement("div",{style:n,className:c},ve.createElement("img",J({},A,{style:gi,ref:S})))});const xi=({src:i,open:e,setOpen:a,setPreview:t,setSelected:s,setSrc:r})=>{const[n,c]=N.useState(""),[f,d]=N.useState("image/jpeg"),[l,h]=N.useState(""),m=N.useRef(null);if(i){const x=new FileReader;x.onload=()=>{c(x.result),d(i.type),h(i.name)},x.readAsDataURL(i)}else return null;const g=()=>{m.current?.cropper&&m.current.cropper.getCroppedCanvas().toBlob(x=>{s({blob:x,name:l}),t(URL.createObjectURL(x))},f),v()},v=()=>{a(!1),r(null)};return o.jsxs(bi,{open:e,setOpen:v,children:[o.jsx(vi,{ref:m,src:n,style:{height:"25rem",width:"100%"},initialAspectRatio:1205/288,aspectRatio:1205/288,guides:!1,viewMode:2,background:!1,dragMode:"move",modal:!1,autoCropArea:1}),o.jsx(Ft,{className:"ml-auto! w-1/4! font-body! text-lg!",variant:"primary",onClick:()=>{g()},children:"Apply"})]})},bi=({children:i,open:e,setOpen:a})=>o.jsx(Gt,{open:e,showClose:!1,title:"Edit Media",contentHeight:"h-[60dvh]",modalMaxWidth:"max-w-3xl!",onOpenChange:a,children:o.jsx("div",{className:"flex flex-col gap-2 p-1",children:i})});function dt({currentUser:i}){const[e,a]=N.useState(null),[t,s]=N.useState(),[r,n]=N.useState(null),[c,f]=N.useState(null),[d,l]=N.useState(null),[h,m]=N.useState(!1),[g,v]=N.useState(i?.username),[x,b]=N.useState(i?.about||""),[k,E]=N.useState("male"),j=Ca(),S=u=>{if(u.preventDefault(),g.length>17){P.error("Student name is too long!");return}if(g.length<3){P.error("Student name is too short!");return}if(x.length>500){P.error("Description is too long!");return}const p={username:g,about:x};if(e){if(i?.userType==="admin"){P.error("Don't change admin avatars");return}p.avatar=e}r&&(p.banner=r.blob),j.mutate(p)};N.useEffect(()=>{if(!e){i?.avatar?s(`/${i?.avatar}`):s(qt);return}const u=URL.createObjectURL(e);return s(u),()=>URL.revokeObjectURL(u)},[e]);const A=u=>{u.preventDefault(),f(u.target.files[0]),m(!0)};return N.useEffect(()=>{if(!r){i?.profileBanner?l(`/${i?.profileBanner}`):l(ja);return}},[r]),o.jsxs("form",{className:"divide-y divide-gray-200 lg:col-span-9 dark:divide-gray-600",onSubmit:S,children:[o.jsxs("div",{className:"px-4 py-2 sm:p-6 md:py-6 lg:pb-8",children:[o.jsxs("div",{children:[o.jsx("h2",{className:"font-medium text-gray-900 text-lg text-stroke-sm leading-6 dark:text-gray-200",children:"Profile"}),o.jsx("p",{className:"mt-1 text-gray-500 text-sm dark:text-amber-600",children:"This information will be displayed publicly so be careful what you share."})]}),o.jsxs("div",{className:"mt-6 flex flex-col lg:flex-row",children:[o.jsxs("div",{className:"grow space-y-6",children:[o.jsxs("div",{children:[o.jsx("label",{htmlFor:"username",className:"mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300",children:"Student Name"}),o.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:o.jsx("input",{value:g,type:"text",name:"username",id:"username",autoComplete:"username",className:"block w-full min-w-0 grow rounded-md border-gray-300 focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200",onChange:u=>{v(u.target.value)}})})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"about",className:"mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300",children:"Profile Description"}),o.jsx("div",{className:"mt-1",children:o.jsx("textarea",{id:"about",name:"about",rows:3,maxLength:500,className:"mt-1 block w-full rounded-md border-gray-300 shadow-xs focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200",value:x,onChange:u=>{b(u.target.value)}})}),o.jsx("p",{className:"mt-2 text-gray-500 text-sm dark:font-normal dark:text-gray-400",children:"Brief description to be shown on your profile."})]})]}),o.jsxs("div",{className:"mt-6 grow lg:mt-0 lg:ml-6 lg:shrink-0 lg:grow-0",children:[o.jsx("p",{className:"mb-3 block font-bold text-gray-700 text-xs uppercase tracking-wide md:text-center dark:font-normal dark:text-gray-300","aria-hidden":"true",children:"Avatar"}),o.jsx("div",{className:"mt-1 lg:hidden",children:o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"inline-block size-12 shrink-0 overflow-hidden rounded-full","aria-hidden":"true",children:o.jsx("img",{className:"size-full rounded-full",src:t,alt:""})}),o.jsx("div",{className:"ml-5 rounded-md shadow-xs",children:o.jsxs("div",{className:"group relative flex items-center justify-center rounded-md border border-gray-300 px-3 py-2 focus-within:ring-2 focus-within:ring-light-blue-500 focus-within:ring-offset-2 hover:bg-gray-50 dark:border-blue-600 dark:bg-blue-700 dark:text-stroke-sm",children:[o.jsxs("label",{htmlFor:"user-photo-mobile",className:"pointer-events-none relative font-medium text-gray-700 text-sm leading-4 dark:font-normal dark:text-white",children:[o.jsx("span",{children:"Change Avatar"}),o.jsx("span",{className:"sr-only",children:" user photo"})]}),o.jsx("input",{id:"user-photo-mobile",name:"user-photo-mobile",type:"file",accept:"image/*",className:"absolute size-full cursor-pointer rounded-md border-gray-300 opacity-0",onChange:u=>a(u.target.files[0])})]})})]})}),o.jsxs("div",{className:"relative hidden overflow-hidden rounded-full lg:block",children:[o.jsx("img",{className:"relative size-40 rounded-full",src:t,alt:""}),o.jsxs("label",{htmlFor:"user-photo",className:"absolute inset-0 flex size-full items-center justify-center bg-black/75 font-medium text-sm text-white opacity-0 focus-within:opacity-100 hover:opacity-100",children:[o.jsx("span",{children:"Change"}),o.jsx("span",{className:"sr-only",children:" user photo"}),o.jsx("input",{type:"file",id:"user-photo",name:"user-photo",accept:"image/*",className:"absolute inset-0 size-full cursor-pointer rounded-md border-gray-300 opacity-0",onChange:u=>a(u.target.files[0])})]})]})]})]}),o.jsxs("div",{className:"mt-6 grid grid-cols-12 gap-6",children:[o.jsxs("div",{className:"col-span-12 sm:col-span-2",children:[o.jsx("label",{htmlFor:"first_name",className:"mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300",children:"Gender"}),o.jsx("select",{id:"location",name:"location",className:"mt-1 block w-full rounded-md border-gray-300 py-2 pr-10 pl-3 text-base focus:border-indigo-500 focus:outline-hidden focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200",value:k,onChange:u=>E(u.target.value),children:o.jsx("option",{value:"male",children:"--"})})]}),o.jsxs("div",{className:"col-span-12",children:[o.jsxs("label",{className:"mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300",children:["Profile Banner Image ",!1]}),o.jsx("div",{className:"flex w-full items-center justify-center",children:o.jsxs("label",{htmlFor:"profile-banner",className:ee(r||i?.profileBanner?"h-fit max-h-52 overflow-hidden border-4 border-dashed lg:h-fit lg:max-h-full dark:border-gray-900":"mb-2 flex h-32 w-full flex-col border-4 border-dashed md:mb-0 dark:border-gray-500","cursor-pointer"),children:[o.jsxs("div",{className:"flex flex-col items-center justify-center",children:[o.jsx("img",{src:d,alt:"",className:ee(r||i?.profileBanner?"w-full object-cover object-center":"mt-5 size-16 text-gray-400 group-hover:text-gray-600","")}),!r&&!i?.profileBanner?o.jsx("p",{className:"text-gray-400 text-sm tracking-wider group-hover:text-gray-600",children:"--"}):null]}),o.jsx("input",{disabled:!1,type:"file",accept:"image/*",id:"profile-banner",name:"profile-banner",className:"hidden",onChange:u=>A(u)})]})}),r?.name&&o.jsx("p",{className:"pt-1 text-center text-gray-400 text-sm tracking-wider group-hover:text-gray-600",children:r.name}),o.jsx(xi,{src:c,setPreview:l,setSelected:n,open:h,setOpen:m,setSrc:f})]})]})]}),o.jsx("div",{className:"mt-4 flex justify-end p-4 sm:px-6",children:o.jsx("button",{type:"submit",className:"ml-5 inline-flex w-1/5 justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-stroke-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2",children:"Save"})})]})}function ji(){const{data:i}=ft(),[e,a]=N.useState("Profile"),t=()=>{window.confirm("Are you sure you want to logout?")&&Zt()},s=c=>e===c,r=[{name:"Profile",current:s("Profile"),icon:ma},{name:"Email",current:s("Email"),icon:ha},{name:"Password",current:s("Password"),icon:ga},{name:"Interface",current:s("Interface"),icon:xa},{name:"Notifications",current:s("Notifications"),icon:ua}],n=()=>{switch(e){case"Profile":return o.jsx(dt,{currentUser:i});case"Email":return o.jsx(ba,{currentUser:i});case"Password":return o.jsx(ka,{currentUser:i});case"Interface":return o.jsx(ya,{currentUser:i});case"Notifications":return o.jsx(Na,{currentUser:i});default:return o.jsx(dt,{currentUser:i})}};return o.jsx("div",{className:"overflow-hidden bg-white text-shadow shadow-sm md:mx-auto md:max-w-6xl md:rounded-lg dark:bg-gray-800 dark:md:border dark:md:border-gray-600",children:o.jsxs("div",{className:"divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-x lg:divide-y-0 dark:divide-gray-600",children:[o.jsx("aside",{className:"pt-1 md:py-6 lg:col-span-3",children:o.jsxs("nav",{className:"space-y-1 ",children:[r.map(c=>o.jsxs("button",{"aria-current":c.current?"page":void 0,className:ee(c.current?"border-teal-500 bg-teal-50 text-teal-700 dark:bg-gray-900 dark:text-teal-300 dark:hover:text-teal-400":"border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-900","group flex w-full cursor-pointer items-center border-l-4 px-3 py-2 font-medium text-sm text-stroke-sm"),onClick:()=>a(c.name),children:[o.jsx(c.icon,{"aria-hidden":"true",className:ee(c.current?"text-teal-500 group-hover:text-teal-500":"text-gray-200 group-hover:text-gray-300","-ml-1 mr-3 size-6 shrink-0")}),o.jsx("span",{className:"truncate px-0.5",children:c.name})]},c.name)),o.jsxs("button",{className:"group flex w-full cursor-pointer items-center border-transparent border-l-4 px-3 py-2 font-medium text-gray-900 text-sm text-stroke-sm hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-900",onClick:t,children:[o.jsx(ea,{className:"-ml-1 mr-3 size-6 shrink-0 text-gray-200 group-hover:text-gray-300","aria-hidden":"true"}),o.jsx("span",{className:"truncate",children:"Sign Out"})]})]})}),o.jsxs("div",{className:"lg:col-span-9",children:[o.jsxs("div",{className:"px-4 py-6 sm:p-6 lg:pb-8",children:[o.jsxs("div",{className:"mb-6",children:[o.jsx("h2",{className:"font-medium text-gray-900 text-lg leading-6 dark:text-gray-100",children:"Account Actions"}),o.jsx("p",{className:"mt-1 text-gray-500 text-sm dark:text-gray-400",children:"Quick actions for your account setup and referrals."})]}),o.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row",children:[!i?.discordID&&o.jsx(Ge,{to:"/discord",children:o.jsxs("button",{type:"button",className:"flex flex-row font-display items-center gap-1.5 rounded-md border border-indigo-700 bg-indigo-600 px-4 py-2 font-medium text-gray-200 text-sm text-stroke-sm shadow-xs hover:bg-indigo-600/75 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2",children:[o.jsx(Kt,{className:"mt-0.5 size-4"}),"Link with Discord"]})}),i?.level>4&&o.jsx(Ge,{to:"/refer",children:o.jsxs("button",{type:"button",className:"flex flex-row font-display items-center gap-1.5 rounded-md border border-blue-700 bg-blue-600 px-4 py-2 font-medium text-gray-200 text-sm text-stroke-sm shadow-xs hover:bg-blue-600/75 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:[o.jsx(Qt,{className:"size-4"}),"Refer a Friend"]})})]}),o.jsx("hr",{className:"mt-6 border-gray-200 dark:border-gray-600"})]}),n()]})]})})}export{ji as default};
