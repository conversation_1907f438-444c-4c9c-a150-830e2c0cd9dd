import { Modal } from "./Modal";

interface ConfirmationModalProps {
    open: boolean;
    setOpen: (open: boolean) => void;
    text: string;
    subtitle?: string;
    effect: () => void;
}

export default function ConfirmationModal({ open, setOpen, text, subtitle, effect }: ConfirmationModalProps) {
    return (
        <Modal
            showClose
            open={open}
            title="WARNING"
            iconBackground="bg-custom-yellow ring-3 ring-gray-900/50"
            Icon={() => (
                <img
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/nlKoyrR.png`}
                    alt=""
                    className="size-10 rounded-full shadow-lg shadow-white invert"
                />
            )}
            onOpenChange={setOpen}
        >
            <div className="mb-6 text-center">
                <p className="text-gray-300 text-lg">{text}</p>
                {subtitle && <p className="text-base text-gray-500">{subtitle}</p>}
            </div>

            <button
                type="button"
                className="darkBlueButtonBGSVG mx-auto flex h-14 w-[60vw] items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs hover:brightness-90 md:mt-3 md:w-1/2 dark:text-slate-200 dark:hover:text-white"
                onClick={effect}
            >
                Yes
            </button>
            <button
                type="button"
                className="skyblueButtonBGSVG mx-auto mt-2 flex h-14 w-[60vw] items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs hover:brightness-90 md:mt-3 md:w-1/2 dark:text-slate-200 dark:hover:text-white"
                onClick={() => setOpen(false)}
            >
                Cancel
            </button>
        </Modal>
    );
}
