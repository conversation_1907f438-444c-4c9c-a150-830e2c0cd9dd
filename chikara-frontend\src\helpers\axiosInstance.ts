import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from "axios";
import { normalStore } from "../app/store/normalStore";
import { ApiError, ForbiddenError, MaintenanceModeError, UnauthorizedError } from "./apiError";
import parseJson from "./parseJson";

export type ApiResponse<T = unknown> = {
    success: boolean;
    data: T | null;
    error: string | null;
    maintenanceMode?: boolean;
};

// Create a configured Axios instance
export const axiosInstance: AxiosInstance = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL || "http://localhost:3000",
    timeout: 15000,
    withCredentials: true,
    headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
    },
});

// Response handler for successful responses
const handleSuccess = <T>(response: AxiosResponse): T => {
    const parsed = response.data as ApiResponse<T>;

    if (!parsed.success) {
        console.error("Response parsing error");
        throw new ApiError(parsed.error || "Request failed");
    }

    return parsed.data !== null ? (parseJson(parsed.data) as T) : (null as T);
};

// Error handler for failed responses
const handleError = (error: unknown) => {
    if (axios.isAxiosError(error)) {
        const responseData = error.response?.data;
        const status = error.response?.status;

        if (responseData?.maintenanceMode) {
            const { setMaintenanceMode } = normalStore.getState();
            setMaintenanceMode(true);
            throw new MaintenanceModeError(responseData?.error || "System is in maintenance mode");
        }

        if (status === 401) {
            throw new UnauthorizedError(responseData?.error || "Your session has expired. Please log in again.");
        }

        if (status === 403) {
            throw new ForbiddenError(responseData?.error || "Your session has expired. Please log in again.");
        }

        const errorMessage = responseData?.error || error.message;
        throw new ApiError(errorMessage, status, error);
    }

    console.error("Unexpected Error:", error);
    throw new ApiError("An unexpected error occurred", undefined, error);
};

// Add response interceptor to handle parsing and errors
axiosInstance.interceptors.response.use(handleSuccess, handleError);

/**
 * Type-safe GET request wrapper
 */
export async function handleGet<TData = unknown>(url: string, config?: AxiosRequestConfig): Promise<TData> {
    return await axiosInstance.get<any, TData>(url, config);
}

/**
 * Type-safe POST request wrapper
 */
export async function handlePost<TData = unknown, TRequest = unknown>(
    url: string,
    data?: TRequest,
    config?: AxiosRequestConfig
): Promise<TData> {
    return await axiosInstance.post<any, TData>(url, data, config);
}

/**
 * Type-safe PUT request wrapper
 */
export async function handlePut<TData = unknown, TRequest = unknown>(
    url: string,
    data?: TRequest,
    config?: AxiosRequestConfig
): Promise<TData> {
    return await axiosInstance.put<any, TData>(url, data, config);
}

/**
 * Type-safe DELETE request wrapper
 */
export async function handleDelete<TData = unknown>(url: string, config?: AxiosRequestConfig): Promise<TData> {
    return await axiosInstance.delete<any, TData>(url, config);
}

// Export convenient API client object
export default { get: handleGet, post: handlePost, put: handlePut, delete: handleDelete };
